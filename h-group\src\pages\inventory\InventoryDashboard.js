const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { formatCurrency, formatNumber, formatDate } = require('../../utils/formatters');

const InventoryDashboard = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [inventoryData, setInventoryData] = useState({
    materials: [],
    products: [],
    lowStockItems: [],
    recentMovements: [],
    stats: {
      totalMaterials: 0,
      totalProducts: 0,
      lowStockCount: 0,
      totalValue: 0
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  // تحميل بيانات المخزون
  useEffect(() => {
    const fetchInventoryData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع بيانات المخزون (المواد الخام فقط)
        const [materials, inventory] = await Promise.all([
          window.api.materials.getAll(),
          window.api.inventory.getAll()
        ]);

        // دمج بيانات المواد مع المخزون
        const materialsWithInventory = materials.map(material => {
          const inventoryItem = inventory.find(inv => inv.material_id === material.id);
          return {
            ...material,
            quantity: inventoryItem?.quantity || 0
          };
        });

        // حساب العناصر منخفضة المخزون
        const lowStockItems = materialsWithInventory.filter(m => m.quantity < m.min_quantity);

        // حساب الإحصائيات
        const totalValue = materialsWithInventory.reduce((sum, m) => sum + (m.quantity * m.cost_per_unit || 0), 0);

        setInventoryData({
          materials: materialsWithInventory,
          lowStockItems,
          recentMovements: [], // سيتم تنفيذها لاحقاً
          stats: {
            totalMaterials: materialsWithInventory.length,
            lowStockCount: lowStockItems.length,
            totalValue
          }
        });
      } catch (error) {
        console.error('خطأ في تحميل بيانات المخزون:', error);
        setError('حدث خطأ أثناء تحميل بيانات المخزون. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryData();
  }, []);

  // تحديث المخزون
  const handleUpdateStock = (itemId, type) => {
    navigate(`/inventory/update/${itemId}?type=${type}`);
  };

  // عرض تفاصيل العنصر
  const handleViewItem = (itemId, type) => {
    navigate(`/materials/${itemId}`);
  };

  // تصدير تقرير المخزون
  const handleExportInventory = async () => {
    try {
      const dataToExport = inventoryData.materials.map(m => ({
        'الاسم': m.name,
        'الفئة': m.category || '-',
        'الوحدة': m.unit || '-',
        'الكمية الحالية': formatNumber(m.quantity || 0),
        'الحد الأدنى': formatNumber(m.min_quantity || 0),
        'التكلفة/وحدة': formatCurrency(m.cost_per_unit || 0),
        'القيمة الإجمالية': formatCurrency((m.quantity || 0) * (m.cost_per_unit || 0)),
        'الحالة': m.quantity < m.min_quantity ? 'منخفض' : 'متوفر'
      }));

      const result = await window.api.exportToExcel(dataToExport, 'تقرير المخزون');

      if (result.success) {
        alert(`تم تصدير تقرير المخزون بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير التقرير');
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير. يرجى المحاولة مرة أخرى.');
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل بيانات المخزون...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger">
        <h4>خطأ</h4>
        <p>{error}</p>
        <button className="btn btn-primary" onClick={() => window.location.reload()}>
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="inventory-dashboard">
      {/* رأس الصفحة */}
      <div className="page-header">
        <div className="page-title">
          <h2><i className="fas fa-boxes"></i> لوحة تحكم المخزون</h2>
          <p>إدارة ومراقبة المخزون والمواد الخام</p>
        </div>
        <div className="page-actions">
          <button className="btn btn-success" onClick={handleExportInventory}>
            <i className="fas fa-file-excel"></i> تصدير تقرير المخزون
          </button>

          {hasPermission('inventory_edit') && (
            <button
              className="btn btn-primary"
              onClick={() => navigate('/inventory/movements')}
            >
              <i className="fas fa-exchange-alt"></i> حركات المخزون
            </button>
          )}
        </div>
      </div>

      {/* إحصائيات المخزون */}
      <div className="stats-row mb-4">
        <div className="stat-card stat-card-primary">
          <div className="stat-icon">
            <i className="fas fa-layer-group"></i>
          </div>
          <div className="stat-content">
            <h3>إجمالي المواد</h3>
            <div className="stat-value">{formatNumber(inventoryData.stats.totalMaterials)}</div>
          </div>
        </div>

        <div className="stat-card stat-card-info">
          <div className="stat-icon">
            <i className="fas fa-boxes"></i>
          </div>
          <div className="stat-content">
            <h3>إجمالي الكمية</h3>
            <div className="stat-value">{formatNumber(inventoryData.materials.reduce((sum, m) => sum + (m.quantity || 0), 0))}</div>
          </div>
        </div>

        <div className="stat-card stat-card-warning">
          <div className="stat-icon">
            <i className="fas fa-exclamation-triangle"></i>
          </div>
          <div className="stat-content">
            <h3>مخزون منخفض</h3>
            <div className="stat-value">{formatNumber(inventoryData.stats.lowStockCount)}</div>
          </div>
        </div>

        <div className="stat-card stat-card-success">
          <div className="stat-icon">
            <i className="fas fa-dollar-sign"></i>
          </div>
          <div className="stat-content">
            <h3>قيمة المخزون</h3>
            <div className="stat-value">{formatCurrency(inventoryData.stats.totalValue)}</div>
          </div>
        </div>
      </div>

      {/* تبويبات المحتوى */}
      <div className="card">
        <div className="card-header">
          <ul className="nav nav-tabs card-header-tabs">
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                onClick={() => setActiveTab('overview')}
              >
                <i className="fas fa-chart-pie"></i> نظرة عامة
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'lowStock' ? 'active' : ''}`}
                onClick={() => setActiveTab('lowStock')}
              >
                <i className="fas fa-exclamation-triangle"></i> مخزون منخفض
                {inventoryData.stats.lowStockCount > 0 && (
                  <span className="badge bg-danger ms-1">{inventoryData.stats.lowStockCount}</span>
                )}
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'movements' ? 'active' : ''}`}
                onClick={() => setActiveTab('movements')}
              >
                <i className="fas fa-exchange-alt"></i> الحركات الأخيرة
              </button>
            </li>
          </ul>
        </div>

        <div className="card-body">
          {/* تبويب النظرة العامة */}
          {activeTab === 'overview' && (
            <div className="overview-tab">
              <div className="row">
                <div className="col-md-6">
                  <div className="card">
                    <div className="card-header">
                      <h5><i className="fas fa-layer-group"></i> المواد الخام</h5>
                    </div>
                    <div className="card-body">
                      <div className="table-responsive">
                        <table className="table table-sm">
                          <thead>
                            <tr>
                              <th>المادة</th>
                              <th>الكمية</th>
                              <th>القيمة</th>
                            </tr>
                          </thead>
                          <tbody>
                            {inventoryData.materials.slice(0, 5).map(material => (
                              <tr key={material.id}>
                                <td>{material.name}</td>
                                <td>{formatNumber(material.quantity || 0)}</td>
                                <td>{formatCurrency((material.quantity || 0) * (material.cost_per_unit || 0))}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      <div className="text-center">
                        <button
                          className="btn btn-sm btn-outline-primary"
                          onClick={() => navigate('/materials')}
                        >
                          عرض جميع المواد
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="col-md-6">
                  <div className="card">
                    <div className="card-header">
                      <h5><i className="fas fa-chart-pie"></i> توزيع المواد</h5>
                    </div>
                    <div className="card-body">
                      <div className="table-responsive">
                        <table className="table table-sm">
                          <thead>
                            <tr>
                              <th>الفئة</th>
                              <th>العدد</th>
                              <th>النسبة</th>
                            </tr>
                          </thead>
                          <tbody>
                            {Object.entries(
                              inventoryData.materials.reduce((acc, material) => {
                                const category = material.category || 'أخرى';
                                acc[category] = (acc[category] || 0) + 1;
                                return acc;
                              }, {})
                            ).map(([category, count]) => (
                              <tr key={category}>
                                <td>{category}</td>
                                <td>{count}</td>
                                <td>{((count / inventoryData.materials.length) * 100).toFixed(1)}%</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      <div className="text-center">
                        <button
                          className="btn btn-sm btn-outline-primary"
                          onClick={() => navigate('/materials')}
                        >
                          عرض جميع المواد
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* تبويب المخزون المنخفض */}
          {activeTab === 'lowStock' && (
            <div className="low-stock-tab">
              {inventoryData.lowStockItems.length === 0 ? (
                <div className="alert alert-success">
                  <i className="fas fa-check-circle"></i>
                  جميع العناصر متوفرة في المخزون!
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead>
                      <tr>
                        <th>النوع</th>
                        <th>الاسم</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {inventoryData.lowStockItems.map(item => (
                        <tr key={`material-${item.id}`} className="table-warning">
                          <td>
                            <span className="badge bg-info">مادة خام</span>
                          </td>
                          <td>{item.name}</td>
                          <td>{formatNumber(item.quantity || 0)}</td>
                          <td>{formatNumber(item.min_quantity || 0)}</td>
                          <td>
                            <span className="badge bg-danger">منخفض</span>
                          </td>
                          <td>
                            <div className="btn-group">
                              <button
                                className="btn btn-sm btn-warning"
                                onClick={() => handleUpdateStock(item.id, 'material')}
                                title="تحديث المخزون"
                              >
                                <i className="fas fa-plus"></i>
                              </button>
                              <button
                                className="btn btn-sm btn-info"
                                onClick={() => handleViewItem(item.id, 'material')}
                                title="عرض التفاصيل"
                              >
                                <i className="fas fa-eye"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* تبويب الحركات الأخيرة */}
          {activeTab === 'movements' && (
            <div className="movements-tab">
              {inventoryData.recentMovements.length === 0 ? (
                <div className="alert alert-info">
                  لا توجد حركات مخزون حديثة
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead>
                      <tr>
                        <th>التاريخ</th>
                        <th>العنصر</th>
                        <th>النوع</th>
                        <th>الكمية</th>
                        <th>السبب</th>
                      </tr>
                    </thead>
                    <tbody>
                      {inventoryData.recentMovements.map(movement => (
                        <tr key={movement.id}>
                          <td>{formatDate(movement.created_at)}</td>
                          <td>{movement.item_name}</td>
                          <td>
                            <span className={`badge ${movement.movement_type === 'in' ? 'bg-success' : 'bg-danger'}`}>
                              {movement.movement_type === 'in' ? 'إدخال' : 'إخراج'}
                            </span>
                          </td>
                          <td>{formatNumber(movement.quantity)}</td>
                          <td>{movement.reason || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              <div className="text-center mt-3">
                <button
                  className="btn btn-outline-primary"
                  onClick={() => navigate('/inventory/movements')}
                >
                  عرض جميع الحركات
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = InventoryDashboard;
