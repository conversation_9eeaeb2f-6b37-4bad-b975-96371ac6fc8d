const React = require('react');
const { useState, useEffect } = React;
const { formatCurrency, formatDate } = require('../../utils/formatters');
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const InvoicesList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [invoices, setInvoices] = useState([]);
  const [filteredInvoices, setFilteredInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });

  // تحميل الفواتير
  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع الفواتير
        const data = await window.api.invoices.getAll();
        setInvoices(data);
        setFilteredInvoices(data);
      } catch (error) {
        console.error('خطأ في تحميل الفواتير:', error);
        setError('حدث خطأ أثناء تحميل الفواتير. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, []);

  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...invoices];

    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(invoice =>
        invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.customer_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // تطبيق فلتر الحالة
    if (statusFilter !== 'all') {
      result = result.filter(invoice => invoice.status === statusFilter);
    }

    // تطبيق فلتر التاريخ
    if (dateRange.startDate && dateRange.endDate) {
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      endDate.setHours(23, 59, 59, 999); // نهاية اليوم

      result = result.filter(invoice => {
        const invoiceDate = new Date(invoice.issue_date);
        return invoiceDate >= startDate && invoiceDate <= endDate;
      });
    }

    setFilteredInvoices(result);
  }, [invoices, searchTerm, statusFilter, dateRange]);

  // تغيير فلتر التاريخ
  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setDateRange({
      startDate: '',
      endDate: ''
    });
  };

  // الانتقال إلى صفحة تفاصيل الفاتورة
  const handleViewInvoice = (id) => {
    navigate(`/invoices/${id}`);
  };

  // إنشاء فاتورة جديدة
  const handleCreateInvoice = () => {
    if (!hasPermission('invoices_create')) {
      alert('ليس لديك صلاحية لإنشاء فاتورة جديدة');
      return;
    }

    // الانتقال إلى صفحة اختيار الطلب أولاً
    navigate('/orders');
  };

  // تصدير الفواتير إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredInvoices.map(invoice => ({
        'رقم الفاتورة': invoice.invoice_number,
        'العميل': invoice.customer_name,
        'تاريخ الإصدار': new Date(invoice.issue_date).toLocaleDateString('ar-SA'),
        'تاريخ الاستحقاق': invoice.due_date ? new Date(invoice.due_date).toLocaleDateString('ar-SA') : '-',
        'المبلغ الإجمالي': invoice.total_amount,
        'المبلغ المدفوع': invoice.paid_amount,
        'المبلغ المتبقي': invoice.total_amount - invoice.paid_amount,
        'نوع الدفع': invoice.payment_type,
        'الحالة': invoice.status
      }));

      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.api.exportToExcel(dataToExport, 'الفواتير');

      if (result.success) {
        alert(`تم تصدير الفواتير بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير الفواتير');
      }
    } catch (error) {
      console.error('خطأ في تصدير الفواتير:', error);
      alert('حدث خطأ أثناء تصدير الفواتير. يرجى المحاولة مرة أخرى.');
    }
  };

  if (loading) {
    return <div className="loading">جاري تحميل الفواتير...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="invoices-list-page">
      <div className="page-header">
        <h2>الفواتير</h2>
        <div className="page-actions">
          {hasPermission('invoices_create') && (
            <button className="btn btn-primary" onClick={handleCreateInvoice}>
              <i className="fas fa-plus"></i> إنشاء فاتورة جديدة
            </button>
          )}

          <button className="btn btn-success" onClick={handleExportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="card mb-4">
        <div className="card-header">فلاتر البحث</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">بحث</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="رقم الفاتورة أو اسم العميل"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="col-md-2">
              <div className="form-group">
                <label className="form-label">الحالة</label>
                <select
                  className="form-control"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">الكل</option>
                  <option value="غير مدفوعة">غير مدفوعة</option>
                  <option value="مدفوعة جزئياً">مدفوعة جزئياً</option>
                  <option value="مدفوعة">مدفوعة</option>
                </select>
              </div>
            </div>

            <div className="col-md-3">
              <div className="form-group">
                <label className="form-label">من تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  name="startDate"
                  value={dateRange.startDate}
                  onChange={handleDateRangeChange}
                />
              </div>
            </div>

            <div className="col-md-3">
              <div className="form-group">
                <label className="form-label">إلى تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  name="endDate"
                  value={dateRange.endDate}
                  onChange={handleDateRangeChange}
                />
              </div>
            </div>
          </div>

          <div className="filter-actions">
            <button className="btn btn-secondary" onClick={handleResetFilters}>
              <i className="fas fa-redo"></i> إعادة تعيين الفلاتر
            </button>
          </div>
        </div>
      </div>

      {/* قائمة الفواتير */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>قائمة الفواتير</h5>
            <span className="badge bg-primary">{filteredInvoices.length} فاتورة</span>
          </div>
        </div>
        <div className="card-body">
          {filteredInvoices.length === 0 ? (
            <div className="alert alert-info">لا توجد فواتير مطابقة للفلاتر المحددة</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>رقم الفاتورة</th>
                    <th>العميل</th>
                    <th>تاريخ الإصدار</th>
                    <th>المبلغ الإجمالي</th>
                    <th>المبلغ المدفوع</th>
                    <th>المبلغ المتبقي</th>
                    <th>نوع الدفع</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredInvoices.map(invoice => (
                    <tr key={invoice.id}>
                      <td>{invoice.invoice_number}</td>
                      <td>{invoice.customer_name}</td>
                      <td>{formatDate(invoice.issue_date)}</td>
                      <td>{formatCurrency(invoice.total_amount)}</td>
                      <td>{formatCurrency(invoice.paid_amount)}</td>
                      <td>{formatCurrency(invoice.total_amount - invoice.paid_amount)}</td>
                      <td>{invoice.payment_type}</td>
                      <td>
                        <span className={`status-badge ${invoice.status === 'مدفوعة' ? 'paid' : invoice.status === 'مدفوعة جزئياً' ? 'partial' : 'unpaid'}`}>
                          {invoice.status}
                        </span>
                      </td>
                      <td>
                        <button
                          className="btn btn-sm btn-info"
                          onClick={() => handleViewInvoice(invoice.id)}
                        >
                          <i className="fas fa-eye"></i> عرض
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = InvoicesList;
