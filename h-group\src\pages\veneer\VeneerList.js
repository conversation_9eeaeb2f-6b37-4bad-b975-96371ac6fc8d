const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { formatCurrency, formatNumber } = require('../../utils/formatters');

const VeneerList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [veneers, setVeneers] = useState([]);
  const [filteredVeneers, setFilteredVeneers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [thicknessFilter, setThicknessFilter] = useState('all');
  const [stockFilter, setStockFilter] = useState('all');

  // تحميل الفونير
  useEffect(() => {
    const fetchVeneers = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع المواد وفلترة الفونير
        const allMaterials = await window.api.materials.getAllWithInventory();
        const data = allMaterials.filter(material =>
          material.category === 'فونير' ||
          material.name.toLowerCase().includes('فونير') ||
          material.name.toLowerCase().includes('خشب مضغوط')
        );
        setVeneers(data);
        setFilteredVeneers(data);
      } catch (error) {
        console.error('خطأ في تحميل الفونير:', error);
        setError('حدث خطأ أثناء تحميل الفونير. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchVeneers();
  }, []);

  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...veneers];

    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(veneer =>
        veneer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (veneer.description && veneer.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // تطبيق فلتر النوع
    if (typeFilter !== 'all') {
      result = result.filter(veneer => veneer.veneer_type === typeFilter);
    }

    // تطبيق فلتر السماكة
    if (thicknessFilter !== 'all') {
      result = result.filter(veneer => veneer.thickness === thicknessFilter);
    }

    // تطبيق فلتر المخزون
    if (stockFilter === 'low') {
      result = result.filter(veneer => veneer.quantity < veneer.min_quantity);
    } else if (stockFilter === 'available') {
      result = result.filter(veneer => veneer.quantity >= veneer.min_quantity);
    }

    setFilteredVeneers(result);
  }, [veneers, searchTerm, typeFilter, thicknessFilter, stockFilter]);

  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
    setTypeFilter('all');
    setThicknessFilter('all');
    setStockFilter('all');
  };

  // الانتقال إلى صفحة تفاصيل الفونير
  const handleViewVeneer = (id) => {
    navigate(`/materials/${id}`);
  };

  // الانتقال إلى صفحة تعديل الفونير
  const handleEditVeneer = (id) => {
    navigate(`/materials/edit/${id}`);
  };

  // تحديث المخزون
  const handleUpdateInventory = (id) => {
    navigate(`/inventory/update/${id}`);
  };

  // حذف الفونير
  const handleDeleteVeneer = async (id) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا النوع من الفونير؟')) {
      return;
    }

    try {
      const result = await window.api.materials.delete(id);
      if (result.success) {
        setVeneers(veneers.filter(v => v.id !== id));
        alert('تم حذف الفونير بنجاح');
      } else {
        throw new Error('فشل في حذف الفونير');
      }
    } catch (error) {
      console.error('خطأ في حذف الفونير:', error);
      alert('حدث خطأ أثناء حذف الفونير. يرجى المحاولة مرة أخرى.');
    }
  };

  // تصدير الفونير إلى Excel
  const handleExportToExcel = async () => {
    try {
      const dataToExport = filteredVeneers.map(veneer => ({
        'اسم الفونير': veneer.name,
        'النوع': veneer.veneer_type || '-',
        'السماكة': veneer.thickness ? `${veneer.thickness} مم` : '-',
        'الأبعاد': veneer.dimensions || '-',
        'التكلفة لكل وحدة': formatCurrency(veneer.cost_per_unit || 0),
        'الكمية المتوفرة': formatNumber(veneer.quantity || 0),
        'الحد الأدنى': formatNumber(veneer.min_quantity || 0),
        'حالة المخزون': veneer.quantity < veneer.min_quantity ? 'منخفض' : 'متوفر'
      }));

      const result = await window.api.exportToExcel(dataToExport, 'الفونير');

      if (result.success) {
        alert(`تم تصدير الفونير بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير الفونير');
      }
    } catch (error) {
      console.error('خطأ في تصدير الفونير:', error);
      alert('حدث خطأ أثناء تصدير الفونير. يرجى المحاولة مرة أخرى.');
    }
  };

  // الحصول على الأنواع المتاحة
  const getAvailableTypes = () => {
    const types = [...new Set(veneers.map(v => v.veneer_type).filter(Boolean))];
    return types;
  };

  // الحصول على السماكات المتاحة
  const getAvailableThicknesses = () => {
    const thicknesses = [...new Set(veneers.map(v => v.thickness).filter(Boolean))];
    return thicknesses.sort((a, b) => a - b);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل الفونير...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger">
        <h4>خطأ</h4>
        <p>{error}</p>
        <button className="btn btn-primary" onClick={() => window.location.reload()}>
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="veneer-list-page">
      {/* رأس الصفحة */}
      <div className="page-header">
        <div className="page-title">
          <h2><i className="fas fa-layer-group"></i> إدارة الفونير</h2>
          <p>إدارة أنواع الفونير والخشب المضغوط</p>
        </div>
        <div className="page-actions">
          {hasPermission('materials_create') && (
            <button
              className="btn btn-primary"
              onClick={() => navigate('/materials/add?category=فونير')}
            >
              <i className="fas fa-plus"></i> إضافة فونير جديد
            </button>
          )}

          <button className="btn btn-success" onClick={handleExportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="stats-row mb-4">
        <div className="stat-card">
          <div className="stat-icon bg-primary">
            <i className="fas fa-layer-group"></i>
          </div>
          <div className="stat-content">
            <h3>إجمالي الأنواع</h3>
            <div className="stat-value">{veneers.length}</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon bg-warning">
            <i className="fas fa-exclamation-triangle"></i>
          </div>
          <div className="stat-content">
            <h3>مخزون منخفض</h3>
            <div className="stat-value">
              {veneers.filter(v => v.quantity < v.min_quantity).length}
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon bg-success">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <h3>متوفر</h3>
            <div className="stat-value">
              {veneers.filter(v => v.quantity >= v.min_quantity).length}
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon bg-info">
            <i className="fas fa-boxes"></i>
          </div>
          <div className="stat-content">
            <h3>إجمالي الكمية</h3>
            <div className="stat-value">
              {formatNumber(veneers.reduce((sum, v) => sum + (v.quantity || 0), 0))}
            </div>
          </div>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="card mb-4">
        <div className="card-header">
          <h5><i className="fas fa-filter"></i> فلاتر البحث</h5>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-3">
              <div className="form-group">
                <label>البحث</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="ابحث في الفونير..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="col-md-3">
              <div className="form-group">
                <label>النوع</label>
                <select
                  className="form-control"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <option value="all">جميع الأنواع</option>
                  {getAvailableTypes().map((type, index) => (
                    <option key={index} value={type}>{type}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="col-md-3">
              <div className="form-group">
                <label>السماكة</label>
                <select
                  className="form-control"
                  value={thicknessFilter}
                  onChange={(e) => setThicknessFilter(e.target.value)}
                >
                  <option value="all">جميع السماكات</option>
                  {getAvailableThicknesses().map((thickness, index) => (
                    <option key={index} value={thickness}>{thickness} مم</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="col-md-3">
              <div className="form-group">
                <label>حالة المخزون</label>
                <select
                  className="form-control"
                  value={stockFilter}
                  onChange={(e) => setStockFilter(e.target.value)}
                >
                  <option value="all">جميع الحالات</option>
                  <option value="available">متوفر</option>
                  <option value="low">منخفض</option>
                </select>
              </div>
            </div>
          </div>

          <div className="filter-actions">
            <button className="btn btn-secondary" onClick={handleResetFilters}>
              <i className="fas fa-redo"></i> إعادة تعيين الفلاتر
            </button>
          </div>
        </div>
      </div>

      {/* قائمة الفونير */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>قائمة الفونير</h5>
            <span className="badge bg-primary">{filteredVeneers.length} نوع</span>
          </div>
        </div>
        <div className="card-body">
          {filteredVeneers.length === 0 ? (
            <div className="alert alert-info">لا توجد أنواع فونير مطابقة للفلاتر المحددة</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>اسم الفونير</th>
                    <th>النوع</th>
                    <th>السماكة</th>
                    <th>الأبعاد</th>
                    <th>التكلفة/وحدة</th>
                    <th>الكمية</th>
                    <th>الحد الأدنى</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredVeneers.map(veneer => (
                    <tr key={veneer.id} className={veneer.quantity < veneer.min_quantity ? 'table-danger' : ''}>
                      <td>
                        <strong>{veneer.name}</strong>
                        {veneer.description && (
                          <small className="d-block text-muted">{veneer.description}</small>
                        )}
                      </td>
                      <td>{veneer.veneer_type || '-'}</td>
                      <td>{veneer.thickness ? `${veneer.thickness} مم` : '-'}</td>
                      <td>{veneer.dimensions || '-'}</td>
                      <td>{formatCurrency(veneer.cost_per_unit || 0)}</td>
                      <td>{formatNumber(veneer.quantity || 0)}</td>
                      <td>{formatNumber(veneer.min_quantity || 0)}</td>
                      <td>
                        <span className={`badge ${veneer.quantity < veneer.min_quantity ? 'bg-danger' : 'bg-success'}`}>
                          {veneer.quantity < veneer.min_quantity ? 'منخفض' : 'متوفر'}
                        </span>
                      </td>
                      <td>
                        <div className="btn-group">
                          <button
                            className="btn btn-sm btn-info"
                            onClick={() => handleViewVeneer(veneer.id)}
                            title="عرض التفاصيل"
                          >
                            <i className="fas fa-eye"></i>
                          </button>

                          {hasPermission('materials_edit') && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handleEditVeneer(veneer.id)}
                              title="تعديل"
                            >
                              <i className="fas fa-edit"></i>
                            </button>
                          )}

                          {hasPermission('inventory_edit') && (
                            <button
                              className="btn btn-sm btn-warning"
                              onClick={() => handleUpdateInventory(veneer.id)}
                              title="تحديث المخزون"
                            >
                              <i className="fas fa-boxes"></i>
                            </button>
                          )}

                          {hasPermission('materials_delete') && (
                            <button
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteVeneer(veneer.id)}
                              title="حذف"
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = VeneerList;
