const React = require('react');
const { useState, useEffect } = React;
const { Link } = require('react-router-dom');
const { formatCurrency, formatDate } = require('../../utils/formatters');

const PayrollDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    currentMonth: {
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      totalWorkers: 0,
      processedPayrolls: 0,
      pendingPayrolls: 0,
      totalGrossSalary: 0,
      totalDeductions: 0,
      totalNetSalary: 0
    },
    recentPayrolls: [],
    pendingAdvances: [],
    upcomingPayments: [],
    workerStats: []
  });

  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  useEffect(() => {
    loadDashboardData();
  }, [selectedMonth, selectedYear]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // تحميل جميع العمال
      const workers = await window.electronAPI.workers.getAll();
      const activeWorkers = workers.filter(w => w.employment_status === 'active' || !w.employment_status);

      // تحميل كشوف المرتبات للشهر المحدد
      const monthlyPayrolls = await window.electronAPI.monthlyPayrolls.getByPeriod(selectedMonth, selectedYear);

      // تحميل السلف المعلقة
      const allAdvances = await window.electronAPI.workerAdvances.getAll();
      const pendingAdvances = allAdvances.filter(advance => 
        advance.status === 'active' && advance.remaining_balance > 0
      );

      // حساب الإحصائيات
      const totalGrossSalary = monthlyPayrolls.reduce((sum, payroll) => sum + (payroll.gross_salary || 0), 0);
      const totalDeductions = monthlyPayrolls.reduce((sum, payroll) => sum + (payroll.total_deductions || 0), 0);
      const totalNetSalary = monthlyPayrolls.reduce((sum, payroll) => sum + (payroll.net_salary || 0), 0);

      // إحصائيات العمال
      const workerStats = activeWorkers.map(worker => {
        const workerPayroll = monthlyPayrolls.find(p => p.worker_id === worker.id);
        const workerAdvances = pendingAdvances.filter(a => a.worker_id === worker.id);
        const totalAdvanceBalance = workerAdvances.reduce((sum, a) => sum + (a.remaining_balance || 0), 0);

        return {
          id: worker.id,
          name: worker.name,
          role: worker.role,
          hasPayroll: !!workerPayroll,
          netSalary: workerPayroll?.net_salary || 0,
          advanceBalance: totalAdvanceBalance,
          paymentStatus: workerPayroll?.payment_status || 'pending'
        };
      });

      setDashboardData({
        currentMonth: {
          month: selectedMonth,
          year: selectedYear,
          totalWorkers: activeWorkers.length,
          processedPayrolls: monthlyPayrolls.length,
          pendingPayrolls: activeWorkers.length - monthlyPayrolls.length,
          totalGrossSalary,
          totalDeductions,
          totalNetSalary
        },
        recentPayrolls: monthlyPayrolls.slice(0, 5),
        pendingAdvances: pendingAdvances.slice(0, 5),
        upcomingPayments: monthlyPayrolls.filter(p => p.payment_status === 'pending').slice(0, 5),
        workerStats
      });

    } catch (error) {
      console.error('خطأ في تحميل بيانات المرتبات:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGeneratePayrolls = async () => {
    try {
      setLoading(true);
      
      // الحصول على العمال الذين لم يتم إنشاء كشوف مرتبات لهم
      const workers = await window.electronAPI.workers.getAll();
      const activeWorkers = workers.filter(w => w.employment_status === 'active' || !w.employment_status);
      const existingPayrolls = await window.electronAPI.monthlyPayrolls.getByPeriod(selectedMonth, selectedYear);
      const processedWorkerIds = existingPayrolls.map(p => p.worker_id);
      
      const workersToProcess = activeWorkers.filter(w => !processedWorkerIds.includes(w.id));

      if (workersToProcess.length === 0) {
        alert('تم إنشاء كشوف المرتبات لجميع العمال في هذا الشهر');
        return;
      }

      // إنشاء كشوف المرتبات
      for (const worker of workersToProcess) {
        try {
          const calculatedPayroll = await window.electronAPI.monthlyPayrolls.calculateForWorker(
            worker.id, selectedMonth, selectedYear
          );
          
          await window.electronAPI.monthlyPayrolls.create(calculatedPayroll);
        } catch (error) {
          console.error(`خطأ في إنشاء كشف مرتب للعامل ${worker.name}:`, error);
        }
      }

      alert(`تم إنشاء كشوف المرتبات لـ ${workersToProcess.length} عامل`);
      loadDashboardData();

    } catch (error) {
      console.error('خطأ في إنشاء كشوف المرتبات:', error);
      alert('حدث خطأ أثناء إنشاء كشوف المرتبات');
    } finally {
      setLoading(false);
    }
  };

  const getMonthName = (month) => {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month - 1];
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل بيانات المرتبات...')
    );
  }

  return React.createElement('div', { className: 'payroll-dashboard professional-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header modern-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-money-check-alt' }),
          ' إدارة المرتبات والأجور'
        ),
        React.createElement('p', { className: 'page-subtitle' }, 
          `نظام شامل لإدارة مرتبات العمال - ${getMonthName(selectedMonth)} ${selectedYear}`
        )
      ),
      React.createElement('div', { className: 'header-actions' },
        React.createElement('div', { className: 'period-selector' },
          React.createElement('select', {
            value: selectedMonth,
            onChange: (e) => setSelectedMonth(parseInt(e.target.value)),
            className: 'form-select'
          },
            Array.from({ length: 12 }, (_, i) => 
              React.createElement('option', { key: i + 1, value: i + 1 }, getMonthName(i + 1))
            )
          ),
          React.createElement('select', {
            value: selectedYear,
            onChange: (e) => setSelectedYear(parseInt(e.target.value)),
            className: 'form-select'
          },
            Array.from({ length: 5 }, (_, i) => {
              const year = new Date().getFullYear() - 2 + i;
              return React.createElement('option', { key: year, value: year }, year);
            })
          )
        ),
        React.createElement('button', {
          className: 'btn btn-primary modern-btn',
          onClick: handleGeneratePayrolls
        },
          React.createElement('i', { className: 'fas fa-calculator' }),
          ' إنشاء كشوف المرتبات'
        )
      )
    ),

    // إحصائيات سريعة
    React.createElement('div', { className: 'stats-grid' },
      React.createElement('div', { className: 'stat-card primary' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-users' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي العمال'),
          React.createElement('div', { className: 'stat-value' }, dashboardData.currentMonth.totalWorkers),
          React.createElement('div', { className: 'stat-detail' }, 
            `${dashboardData.currentMonth.processedPayrolls} تم معالجتهم`
          )
        )
      ),
      React.createElement('div', { className: 'stat-card success' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-money-bill-wave' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي الرواتب'),
          React.createElement('div', { className: 'stat-value' }, 
            formatCurrency(dashboardData.currentMonth.totalGrossSalary)
          ),
          React.createElement('div', { className: 'stat-detail' }, 'الراتب الإجمالي')
        )
      ),
      React.createElement('div', { className: 'stat-card warning' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-minus-circle' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي الخصومات'),
          React.createElement('div', { className: 'stat-value' }, 
            formatCurrency(dashboardData.currentMonth.totalDeductions)
          ),
          React.createElement('div', { className: 'stat-detail' }, 'خصومات وسلف')
        )
      ),
      React.createElement('div', { className: 'stat-card info' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-hand-holding-usd' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'صافي المرتبات'),
          React.createElement('div', { className: 'stat-value' }, 
            formatCurrency(dashboardData.currentMonth.totalNetSalary)
          ),
          React.createElement('div', { className: 'stat-detail' }, 'المبلغ المستحق')
        )
      )
    ),

    // الأقسام الرئيسية
    React.createElement('div', { className: 'dashboard-sections' },
      React.createElement('div', { className: 'row' },
        // قائمة العمال وحالة المرتبات
        React.createElement('div', { className: 'col-md-8' },
          React.createElement('div', { className: 'modern-card glass-card' },
            React.createElement('div', { className: 'card-header modern-card-header' },
              React.createElement('h3', { className: 'card-title' },
                React.createElement('i', { className: 'fas fa-list-alt' }),
                ' حالة مرتبات العمال'
              ),
              React.createElement('div', { className: 'card-actions' },
                React.createElement(Link, {
                  to: '/payroll/monthly',
                  className: 'btn btn-sm btn-outline-primary'
                }, 'عرض التفاصيل')
              )
            ),
            React.createElement('div', { className: 'card-body' },
              React.createElement('div', { className: 'table-responsive' },
                React.createElement('table', { className: 'modern-table professional-table' },
                  React.createElement('thead', null,
                    React.createElement('tr', null,
                      React.createElement('th', null, 'العامل'),
                      React.createElement('th', null, 'الدور'),
                      React.createElement('th', null, 'صافي الراتب'),
                      React.createElement('th', null, 'رصيد السلف'),
                      React.createElement('th', null, 'الحالة'),
                      React.createElement('th', null, 'الإجراءات')
                    )
                  ),
                  React.createElement('tbody', null,
                    dashboardData.workerStats.slice(0, 10).map(worker =>
                      React.createElement('tr', { key: worker.id },
                        React.createElement('td', null, worker.name),
                        React.createElement('td', null, worker.role || '-'),
                        React.createElement('td', null, formatCurrency(worker.netSalary)),
                        React.createElement('td', null, 
                          worker.advanceBalance > 0 
                            ? React.createElement('span', { className: 'text-warning' }, formatCurrency(worker.advanceBalance))
                            : '-'
                        ),
                        React.createElement('td', null,
                          React.createElement('span', { 
                            className: `status-badge ${worker.hasPayroll ? 'success' : 'warning'}` 
                          }, worker.hasPayroll ? 'تم المعالجة' : 'معلق')
                        ),
                        React.createElement('td', null,
                          React.createElement('div', { className: 'action-buttons' },
                            React.createElement(Link, {
                              to: `/workers/${worker.id}/payroll`,
                              className: 'action-btn info-btn',
                              title: 'عرض تفاصيل المرتب'
                            },
                              React.createElement('i', { className: 'fas fa-eye' })
                            ),
                            !worker.hasPayroll && React.createElement('button', {
                              className: 'action-btn primary-btn',
                              title: 'إنشاء كشف مرتب',
                              onClick: async () => {
                                try {
                                  const calculatedPayroll = await window.electronAPI.monthlyPayrolls.calculateForWorker(
                                    worker.id, selectedMonth, selectedYear
                                  );
                                  await window.electronAPI.monthlyPayrolls.create(calculatedPayroll);
                                  loadDashboardData();
                                  alert('تم إنشاء كشف المرتب بنجاح');
                                } catch (error) {
                                  console.error('خطأ في إنشاء كشف المرتب:', error);
                                  alert('حدث خطأ أثناء إنشاء كشف المرتب');
                                }
                              }
                            },
                              React.createElement('i', { className: 'fas fa-plus' })
                            )
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
          )
        ),

        // الروابط السريعة
        React.createElement('div', { className: 'col-md-4' },
          React.createElement('div', { className: 'modern-card glass-card' },
            React.createElement('div', { className: 'card-header modern-card-header' },
              React.createElement('h3', { className: 'card-title' },
                React.createElement('i', { className: 'fas fa-link' }),
                ' الوصول السريع'
              )
            ),
            React.createElement('div', { className: 'card-body' },
              React.createElement('div', { className: 'quick-links' },
                React.createElement(Link, {
                  to: '/payroll/advances',
                  className: 'quick-link-item'
                },
                  React.createElement('i', { className: 'fas fa-hand-holding-usd' }),
                  React.createElement('span', null, 'إدارة السلف'),
                  React.createElement('div', { className: 'link-badge' }, dashboardData.pendingAdvances.length)
                ),
                React.createElement(Link, {
                  to: '/payroll/deductions',
                  className: 'quick-link-item'
                },
                  React.createElement('i', { className: 'fas fa-minus-circle' }),
                  React.createElement('span', null, 'الخصومات والغرامات')
                ),
                React.createElement(Link, {
                  to: '/payroll/bonuses',
                  className: 'quick-link-item'
                },
                  React.createElement('i', { className: 'fas fa-gift' }),
                  React.createElement('span', null, 'المكافآت والحوافز')
                ),
                React.createElement(Link, {
                  to: '/payroll/reports',
                  className: 'quick-link-item'
                },
                  React.createElement('i', { className: 'fas fa-chart-bar' }),
                  React.createElement('span', null, 'تقارير المرتبات')
                ),
                React.createElement(Link, {
                  to: '/workers',
                  className: 'quick-link-item'
                },
                  React.createElement('i', { className: 'fas fa-users-cog' }),
                  React.createElement('span', null, 'إدارة العمال')
                )
              )
            )
          )
        )
      )
    )
  );
};

module.exports = PayrollDashboard;
