const React = require('react');
const { useState, useEffect } = React;
const { formatCurrency, formatDate } = require('../../utils/formatters');

const AdvancesManagement = () => {
  const [advances, setAdvances] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [filteredAdvances, setFilteredAdvances] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingAdvance, setEditingAdvance] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const [formData, setFormData] = useState({
    worker_id: '',
    amount: '',
    reason: '',
    repayment_method: 'monthly_deduction',
    monthly_deduction_amount: '',
    approved_by: '',
    notes: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterAdvances();
  }, [advances, searchTerm, statusFilter]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [advancesData, workersData] = await Promise.all([
        window.electronAPI.workerAdvances.getAll(),
        window.electronAPI.workers.getAll()
      ]);
      
      setAdvances(advancesData);
      setWorkers(workersData.filter(w => w.employment_status === 'active' || !w.employment_status));
    } catch (error) {
      console.error('خطأ في تحميل بيانات السلف:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterAdvances = () => {
    let filtered = advances;

    if (searchTerm) {
      filtered = filtered.filter(advance =>
        advance.worker_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        advance.reason?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(advance => advance.status === statusFilter);
    }

    setFilteredAdvances(filtered);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const advanceData = {
        ...formData,
        amount: parseFloat(formData.amount),
        monthly_deduction_amount: parseFloat(formData.monthly_deduction_amount) || 0
      };

      if (editingAdvance) {
        await window.electronAPI.workerAdvances.update(editingAdvance.id, advanceData);
      } else {
        await window.electronAPI.workerAdvances.create(advanceData);
      }

      setShowForm(false);
      setEditingAdvance(null);
      resetForm();
      loadData();
      alert(editingAdvance ? 'تم تحديث السلفة بنجاح' : 'تم إنشاء السلفة بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ السلفة:', error);
      alert('حدث خطأ أثناء حفظ السلفة');
    }
  };

  const resetForm = () => {
    setFormData({
      worker_id: '',
      amount: '',
      reason: '',
      repayment_method: 'monthly_deduction',
      monthly_deduction_amount: '',
      approved_by: '',
      notes: ''
    });
  };

  const handleEdit = (advance) => {
    setEditingAdvance(advance);
    setFormData({
      worker_id: advance.worker_id,
      amount: advance.amount.toString(),
      reason: advance.reason || '',
      repayment_method: advance.repayment_method || 'monthly_deduction',
      monthly_deduction_amount: advance.monthly_deduction_amount?.toString() || '',
      approved_by: advance.approved_by || '',
      notes: advance.notes || ''
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (confirm('هل أنت متأكد من حذف هذه السلفة؟')) {
      try {
        await window.electronAPI.workerAdvances.delete(id);
        loadData();
        alert('تم حذف السلفة بنجاح');
      } catch (error) {
        console.error('خطأ في حذف السلفة:', error);
        alert('حدث خطأ أثناء حذف السلفة');
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      'active': { class: 'success', text: 'نشط' },
      'paid_off': { class: 'info', text: 'مسدد' },
      'cancelled': { class: 'danger', text: 'ملغي' }
    };
    const statusInfo = statusMap[status] || { class: 'secondary', text: status };
    return React.createElement('span', { 
      className: `status-badge ${statusInfo.class}` 
    }, statusInfo.text);
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل بيانات السلف...')
    );
  }

  return React.createElement('div', { className: 'advances-management professional-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header modern-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-hand-holding-usd' }),
          ' إدارة السلف والدفعات المقدمة'
        ),
        React.createElement('p', { className: 'page-subtitle' }, 'إدارة شاملة لسلف العمال والدفعات المقدمة')
      ),
      React.createElement('div', { className: 'page-actions' },
        React.createElement('button', {
          className: 'btn btn-primary modern-btn',
          onClick: () => {
            setEditingAdvance(null);
            resetForm();
            setShowForm(true);
          }
        },
          React.createElement('i', { className: 'fas fa-plus' }),
          ' سلفة جديدة'
        )
      )
    ),

    // الفلاتر
    React.createElement('div', { className: 'filters-section' },
      React.createElement('div', { className: 'row' },
        React.createElement('div', { className: 'col-md-6' },
          React.createElement('div', { className: 'search-box' },
            React.createElement('i', { className: 'fas fa-search' }),
            React.createElement('input', {
              type: 'text',
              placeholder: 'البحث في السلف...',
              value: searchTerm,
              onChange: (e) => setSearchTerm(e.target.value),
              className: 'form-control'
            })
          )
        ),
        React.createElement('div', { className: 'col-md-3' },
          React.createElement('select', {
            value: statusFilter,
            onChange: (e) => setStatusFilter(e.target.value),
            className: 'form-select'
          },
            React.createElement('option', { value: 'all' }, 'جميع الحالات'),
            React.createElement('option', { value: 'active' }, 'نشط'),
            React.createElement('option', { value: 'paid_off' }, 'مسدد'),
            React.createElement('option', { value: 'cancelled' }, 'ملغي')
          )
        )
      )
    ),

    // قائمة السلف
    React.createElement('div', { className: 'modern-card glass-card' },
      React.createElement('div', { className: 'card-header modern-card-header' },
        React.createElement('h3', { className: 'card-title' },
          React.createElement('i', { className: 'fas fa-list' }),
          ' قائمة السلف والدفعات المقدمة'
        ),
        React.createElement('div', { className: 'card-stats' },
          React.createElement('span', { className: 'status-badge primary' }, 
            `${filteredAdvances.length} سلفة`
          )
        )
      ),
      React.createElement('div', { className: 'card-body' },
        filteredAdvances.length === 0 
          ? React.createElement('div', { className: 'empty-state' },
              React.createElement('i', { className: 'fas fa-hand-holding-usd' }),
              React.createElement('h3', null, 'لا توجد سلف'),
              React.createElement('p', null, 'لم يتم العثور على سلف مطابقة للفلاتر المحددة')
            )
          : React.createElement('div', { className: 'table-responsive' },
              React.createElement('table', { className: 'modern-table professional-table' },
                React.createElement('thead', null,
                  React.createElement('tr', null,
                    React.createElement('th', null, 'العامل'),
                    React.createElement('th', null, 'المبلغ'),
                    React.createElement('th', null, 'السبب'),
                    React.createElement('th', null, 'طريقة السداد'),
                    React.createElement('th', null, 'الرصيد المتبقي'),
                    React.createElement('th', null, 'الحالة'),
                    React.createElement('th', null, 'تاريخ السلفة'),
                    React.createElement('th', null, 'الإجراءات')
                  )
                ),
                React.createElement('tbody', null,
                  filteredAdvances.map(advance =>
                    React.createElement('tr', { key: advance.id },
                      React.createElement('td', null,
                        React.createElement('div', { className: 'worker-info' },
                          React.createElement('strong', null, advance.worker_name),
                          advance.employee_number && React.createElement('small', null, 
                            React.createElement('br'),
                            `رقم: ${advance.employee_number}`
                          )
                        )
                      ),
                      React.createElement('td', null, formatCurrency(advance.amount)),
                      React.createElement('td', null, advance.reason || '-'),
                      React.createElement('td', null,
                        advance.repayment_method === 'monthly_deduction' ? 'خصم شهري' :
                        advance.repayment_method === 'lump_sum' ? 'دفعة واحدة' :
                        advance.repayment_method === 'installments' ? 'أقساط' : advance.repayment_method
                      ),
                      React.createElement('td', null,
                        React.createElement('span', { 
                          className: advance.remaining_balance > 0 ? 'text-warning' : 'text-success' 
                        }, formatCurrency(advance.remaining_balance || 0))
                      ),
                      React.createElement('td', null, getStatusBadge(advance.status)),
                      React.createElement('td', null, formatDate(advance.advance_date)),
                      React.createElement('td', null,
                        React.createElement('div', { className: 'action-buttons' },
                          React.createElement('button', {
                            className: 'action-btn info-btn',
                            onClick: () => handleEdit(advance),
                            title: 'تعديل السلفة'
                          },
                            React.createElement('i', { className: 'fas fa-edit' })
                          ),
                          React.createElement('button', {
                            className: 'action-btn danger-btn',
                            onClick: () => handleDelete(advance.id),
                            title: 'حذف السلفة'
                          },
                            React.createElement('i', { className: 'fas fa-trash' })
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
      )
    ),

    // نموذج إضافة/تعديل السلفة
    showForm && React.createElement('div', { className: 'modal-overlay' },
      React.createElement('div', { className: 'modal-content large' },
        React.createElement('div', { className: 'modal-header' },
          React.createElement('h3', null, editingAdvance ? 'تعديل السلفة' : 'سلفة جديدة'),
          React.createElement('button', {
            className: 'close-btn',
            onClick: () => setShowForm(false)
          }, '×')
        ),
        React.createElement('form', { onSubmit: handleSubmit },
          React.createElement('div', { className: 'modal-body' },
            React.createElement('div', { className: 'row' },
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('div', { className: 'form-group' },
                  React.createElement('label', null, 'العامل *'),
                  React.createElement('select', {
                    value: formData.worker_id,
                    onChange: (e) => setFormData({...formData, worker_id: e.target.value}),
                    className: 'form-select',
                    required: true
                  },
                    React.createElement('option', { value: '' }, 'اختر العامل'),
                    workers.map(worker =>
                      React.createElement('option', { key: worker.id, value: worker.id }, 
                        `${worker.name} ${worker.employee_number ? `(${worker.employee_number})` : ''}`
                      )
                    )
                  )
                )
              ),
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('div', { className: 'form-group' },
                  React.createElement('label', null, 'مبلغ السلفة (د.ل) *'),
                  React.createElement('input', {
                    type: 'number',
                    step: '0.01',
                    value: formData.amount,
                    onChange: (e) => setFormData({...formData, amount: e.target.value}),
                    className: 'form-control',
                    required: true
                  })
                )
              )
            ),
            React.createElement('div', { className: 'row' },
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('div', { className: 'form-group' },
                  React.createElement('label', null, 'طريقة السداد'),
                  React.createElement('select', {
                    value: formData.repayment_method,
                    onChange: (e) => setFormData({...formData, repayment_method: e.target.value}),
                    className: 'form-select'
                  },
                    React.createElement('option', { value: 'monthly_deduction' }, 'خصم شهري'),
                    React.createElement('option', { value: 'lump_sum' }, 'دفعة واحدة'),
                    React.createElement('option', { value: 'installments' }, 'أقساط')
                  )
                )
              ),
              React.createElement('div', { className: 'col-md-6' },
                React.createElement('div', { className: 'form-group' },
                  React.createElement('label', null, 'مبلغ الخصم الشهري (د.ل)'),
                  React.createElement('input', {
                    type: 'number',
                    step: '0.01',
                    value: formData.monthly_deduction_amount,
                    onChange: (e) => setFormData({...formData, monthly_deduction_amount: e.target.value}),
                    className: 'form-control'
                  })
                )
              )
            ),
            React.createElement('div', { className: 'form-group' },
              React.createElement('label', null, 'سبب السلفة'),
              React.createElement('input', {
                type: 'text',
                value: formData.reason,
                onChange: (e) => setFormData({...formData, reason: e.target.value}),
                className: 'form-control',
                placeholder: 'مثال: ظروف طارئة، مصاريف طبية، إلخ'
              })
            ),
            React.createElement('div', { className: 'form-group' },
              React.createElement('label', null, 'الموافق على السلفة'),
              React.createElement('input', {
                type: 'text',
                value: formData.approved_by,
                onChange: (e) => setFormData({...formData, approved_by: e.target.value}),
                className: 'form-control',
                placeholder: 'اسم المسؤول الموافق'
              })
            ),
            React.createElement('div', { className: 'form-group' },
              React.createElement('label', null, 'ملاحظات'),
              React.createElement('textarea', {
                value: formData.notes,
                onChange: (e) => setFormData({...formData, notes: e.target.value}),
                className: 'form-control',
                rows: 3,
                placeholder: 'ملاحظات إضافية...'
              })
            )
          ),
          React.createElement('div', { className: 'modal-footer' },
            React.createElement('button', {
              type: 'button',
              className: 'btn btn-secondary',
              onClick: () => setShowForm(false)
            }, 'إلغاء'),
            React.createElement('button', {
              type: 'submit',
              className: 'btn btn-primary'
            }, editingAdvance ? 'تحديث' : 'حفظ')
          )
        )
      )
    )
  );
};

module.exports = AdvancesManagement;
