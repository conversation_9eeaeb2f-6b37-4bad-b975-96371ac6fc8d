const React = require('react');
const { NavLink, useLocation } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { useState } = React;

const Sidebar = () => {
  const { currentUser, hasPermission } = useAuth();
  const location = useLocation();

  // حالات القوائم المنسدلة
  const [expandedMenus, setExpandedMenus] = useState({
    production: false,
    hr: false,
    financial: false
  });

  // التحقق من وجود المستخدم وصلاحياته
  if (!currentUser) return null;

  // دالة لتبديل حالة القائمة المنسدلة
  const toggleMenu = (menuKey) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  // دالة للتحقق من النشاط
  const isActiveGroup = (paths) => {
    return paths.some(path => location.pathname.startsWith(path));
  };

  return React.createElement('aside', { className: 'sidebar' },
    React.createElement('div', { className: 'sidebar-menu' },
      // لوحة التحكم
      React.createElement(NavLink, {
        to: '/',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item',
        end: true
      },
        React.createElement('i', { className: 'fas fa-tachometer-alt' }),
        React.createElement('span', null, 'لوحة التحكم')
      ),

      // الطلبات المخصصة
      React.createElement(NavLink, {
        to: '/orders',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-clipboard-list' }),
        React.createElement('span', null, 'الطلبات المخصصة')
      ),

      // العملاء
      React.createElement(NavLink, {
        to: '/customers',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-users' }),
        React.createElement('span', null, 'العملاء')
      ),

      // إدارة الإنتاج (دمج: المواد + الفونير + المخزون + الحجوزات)
      React.createElement(NavLink, {
        to: '/production',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-industry' }),
        React.createElement('span', null, 'إدارة الإنتاج')
      ),

      // الموارد البشرية (دمج: العمال + المرتبات)
      React.createElement(NavLink, {
        to: '/hr',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-users-cog' }),
        React.createElement('span', null, 'الموارد البشرية')
      ),

      // الإدارة المالية (دمج: الفواتير + المصروفات)
      React.createElement(NavLink, {
        to: '/financial',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-chart-line' }),
        React.createElement('span', null, 'الإدارة المالية')
      ),

      // التقارير
      React.createElement(NavLink, {
        to: '/reports',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-chart-bar' }),
        React.createElement('span', null, 'التقارير')
      ),

      // الإعدادات
      currentUser.role === 'admin' && React.createElement(NavLink, {
        to: '/settings',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-cog' }),
        React.createElement('span', null, 'الإعدادات')
      )
    )
  );
};

module.exports = Sidebar;
