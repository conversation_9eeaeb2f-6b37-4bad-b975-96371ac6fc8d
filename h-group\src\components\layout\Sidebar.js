const React = require('react');
const { NavLink } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const Sidebar = () => {
  const { currentUser, hasPermission } = useAuth();

  // التحقق من وجود المستخدم وصلاحياته
  if (!currentUser) return null;

  return React.createElement('aside', { className: 'sidebar' },
    React.createElement('div', { className: 'sidebar-menu' },
      // لوحة التحكم
      React.createElement(NavLink, {
        to: '/',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item',
        end: true
      },
        React.createElement('i', { className: 'fas fa-tachometer-alt' }),
        React.createElement('span', null, 'لوحة التحكم')
      ),

      // لوحة التحكم المتقدمة
      React.createElement(NavLink, {
        to: '/advanced-dashboard',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-chart-line' }),
        React.createElement('span', null, 'لوحة التحكم المتقدمة')
      ),

      // الطلبات
      React.createElement(NavLink, {
        to: '/orders',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-shopping-cart' }),
        React.createElement('span', null, 'الطلبات')
      ),

      // العملاء
      React.createElement(NavLink, {
        to: '/customers',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-users' }),
        React.createElement('span', null, 'العملاء')
      ),

      // المنتجات
      React.createElement(NavLink, {
        to: '/products',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-couch' }),
        React.createElement('span', null, 'المنتجات')
      ),

      // المواد الخام
      React.createElement(NavLink, {
        to: '/materials',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-boxes' }),
        React.createElement('span', null, 'المواد الخام')
      ),

      // حجوزات المواد
      React.createElement(NavLink, {
        to: '/materials/reservations',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-bookmark' }),
        React.createElement('span', null, 'حجوزات المواد')
      ),

      // الفونير
      React.createElement(NavLink, {
        to: '/veneer',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-layer-group' }),
        React.createElement('span', null, 'الفونير')
      ),

      // المخزون
      React.createElement(NavLink, {
        to: '/inventory',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-warehouse' }),
        React.createElement('span', null, 'المخزون')
      ),

      // العمال
      React.createElement(NavLink, {
        to: '/workers',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-hard-hat' }),
        React.createElement('span', null, 'العمال والمصممين')
      ),

      // المصروفات
      React.createElement(NavLink, {
        to: '/expenses',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-industry' }),
        React.createElement('span', null, 'المصنع والتكاليف')
      ),

      // الفواتير
      React.createElement(NavLink, {
        to: '/invoices',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-file-invoice-dollar' }),
        React.createElement('span', null, 'الفواتير')
      ),

      // التقارير
      React.createElement(NavLink, {
        to: '/reports',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-chart-bar' }),
        React.createElement('span', null, 'التقارير')
      ),

      // الإعدادات
      currentUser.role === 'admin' && React.createElement(NavLink, {
        to: '/settings',
        className: ({ isActive }) => isActive ? 'sidebar-item active' : 'sidebar-item'
      },
        React.createElement('i', { className: 'fas fa-cog' }),
        React.createElement('span', null, 'الإعدادات')
      )
    )
  );
};

module.exports = Sidebar;
