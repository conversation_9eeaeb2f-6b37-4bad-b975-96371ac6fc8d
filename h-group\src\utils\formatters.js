// مساعدات التنسيق للتطبيق

/**
 * تنسيق العملة إلى دينار ليبي
 * @param {number} amount - المبلغ
 * @returns {string} - المبلغ منسق مع العملة
 */
const formatCurrency = (amount) => {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return '0 د.ل';
  }
  
  // تنسيق الرقم بدون فواصل عشرية إضافية
  const formattedNumber = Math.round(amount).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return `${formattedNumber} د.ل`;
};

/**
 * تنسيق الأرقام بالتنسيق المطلوب (123,456)
 * @param {number} number - الرقم
 * @returns {string} - الرقم منسق
 */
const formatNumber = (number) => {
  if (typeof number !== 'number' || isNaN(number)) {
    return '0';
  }
  
  // تنسيق الرقم مع الفواصل
  return Math.round(number).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * تنسيق التاريخ إلى التنسيق الرقمي dd/mm/yyyy
 * @param {Date|string} date - التاريخ
 * @returns {string} - التاريخ منسق
 */
const formatDate = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const day = String(dateObj.getDate()).padStart(2, '0');
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const year = dateObj.getFullYear();
  
  return `${day}/${month}/${year}`;
};

/**
 * تنسيق التاريخ والوقت
 * @param {Date|string} date - التاريخ
 * @returns {string} - التاريخ والوقت منسق
 */
const formatDateTime = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const day = String(dateObj.getDate()).padStart(2, '0');
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const year = dateObj.getFullYear();
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  
  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

/**
 * تنسيق الوقت فقط
 * @param {Date|string} date - التاريخ
 * @returns {string} - الوقت منسق
 */
const formatTime = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  
  return `${hours}:${minutes}:${seconds}`;
};

/**
 * تنسيق النسبة المئوية
 * @param {number} value - القيمة
 * @param {number} decimals - عدد الخانات العشرية
 * @returns {string} - النسبة منسقة
 */
const formatPercentage = (value, decimals = 1) => {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0%';
  }
  
  return `${value.toFixed(decimals)}%`;
};

/**
 * تنسيق الأرقام الكبيرة (مليون، ألف)
 * @param {number} value - القيمة
 * @returns {string} - الرقم منسق
 */
const formatLargeNumber = (value) => {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0';
  }
  
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}م`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}ك`;
  }
  
  return formatNumber(value);
};

/**
 * تحويل التاريخ من التنسيق الرقمي إلى كائن Date
 * @param {string} dateString - التاريخ بالتنسيق dd/mm/yyyy
 * @returns {Date|null} - كائن التاريخ أو null
 */
const parseDate = (dateString) => {
  if (!dateString || typeof dateString !== 'string') return null;
  
  const parts = dateString.split('/');
  if (parts.length !== 3) return null;
  
  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10) - 1; // الشهر يبدأ من 0
  const year = parseInt(parts[2], 10);
  
  const date = new Date(year, month, day);
  
  // التحقق من صحة التاريخ
  if (date.getDate() !== day || date.getMonth() !== month || date.getFullYear() !== year) {
    return null;
  }
  
  return date;
};

/**
 * تنسيق حجم الملف
 * @param {number} bytes - الحجم بالبايت
 * @returns {string} - الحجم منسق
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

module.exports = {
  formatCurrency,
  formatNumber,
  formatDate,
  formatDateTime,
  formatTime,
  formatPercentage,
  formatLargeNumber,
  parseDate,
  formatFileSize
};
