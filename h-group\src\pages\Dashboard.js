const React = require('react');
const { useState, useEffect } = React;

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    orders: { total: 0, pending: 0, inProgress: 0, completed: 0 },
    materials: { lowStock: 0, totalValue: 0, reserved: 0 },
    production: { activeStages: 0, completionRate: 0, efficiency: 0 },
    quality: { passRate: 0, defectsCount: 0, checksToday: 0 },
    financial: { monthlyRevenue: 0, monthlyExpenses: 0, profit: 0 },
    workers: { active: 0, efficiency: 0, workload: 0 }
  });

  const [recentOrders, setRecentOrders] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [productionChart, setProductionChart] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // تحديث كل 30 ثانية
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // تحميل بيانات الطلبات المخصصة
      const orders = await window.electronAPI.orders.getAll();
      const ordersStats = {
        total: orders.length,
        pending: orders.filter(o => o.status === 'جديد').length,
        inProgress: orders.filter(o => o.status === 'قيد التنفيذ').length,
        completed: orders.filter(o => o.status === 'مكتمل').length
      };

      // تحميل بيانات المواد والحجوزات
      const materials = await window.electronAPI.materials.getAll();
      const inventory = await window.electronAPI.inventory.getAll();
      const lowStockMaterials = inventory.filter(item => 
        item.quantity <= item.min_quantity
      );

      // حساب المواد المحجوزة
      let totalReserved = 0;
      for (const material of materials) {
        try {
          const reservations = await window.electronAPI.materialReservations.getByMaterialId(material.id);
          const activeReservations = reservations.filter(r => r.status === 'نشط');
          totalReserved += activeReservations.reduce((sum, r) => sum + r.reserved_quantity, 0);
        } catch (error) {
          console.warn('خطأ في تحميل حجوزات المادة:', material.name);
        }
      }

      // تحميل بيانات الإنتاج
      let productionData = { activeStages: 0, completionRate: 0, efficiency: 0 };
      try {
        const allStages = [];
        for (const order of orders) {
          const stages = await window.electronAPI.productionStages.getByOrderId(order.id);
          allStages.push(...stages);
        }

        const activeStages = allStages.filter(stage => stage.status === 'قيد التنفيذ');
        const completedStages = allStages.filter(stage => stage.status === 'مكتملة');
        const completionRate = allStages.length > 0 
          ? (completedStages.length / allStages.length) * 100 
          : 0;

        // حساب الكفاءة بناءً على الوقت المقدر مقابل الفعلي
        const stagesWithTime = allStages.filter(s => s.estimated_hours > 0 && s.actual_hours > 0);
        const efficiency = stagesWithTime.length > 0
          ? stagesWithTime.reduce((sum, s) => sum + (s.estimated_hours / s.actual_hours), 0) / stagesWithTime.length * 100
          : 85;

        productionData = {
          activeStages: activeStages.length,
          completionRate: Math.round(completionRate),
          efficiency: Math.round(efficiency)
        };
      } catch (error) {
        console.warn('خطأ في تحميل بيانات الإنتاج:', error);
      }

      // تحميل بيانات الجودة
      let qualityData = { passRate: 95, defectsCount: 0, checksToday: 0 };
      try {
        const today = new Date().toISOString().split('T')[0];
        let allChecks = [];
        let allDefects = [];

        for (const order of orders) {
          const checks = await window.electronAPI.qualityChecks.getByOrderId(order.id);
          const defects = await window.electronAPI.defectsRepairs.getByOrderId(order.id);
          allChecks.push(...checks);
          allDefects.push(...defects);
        }

        const todayChecks = allChecks.filter(c => c.check_date?.startsWith(today));
        const passedChecks = allChecks.filter(c => c.status === 'مقبول');
        const passRate = allChecks.length > 0 ? (passedChecks.length / allChecks.length) * 100 : 95;

        qualityData = {
          passRate: Math.round(passRate),
          defectsCount: allDefects.length,
          checksToday: todayChecks.length
        };
      } catch (error) {
        console.warn('خطأ في تحميل بيانات الجودة:', error);
      }

      // تحميل البيانات المالية
      let financialData = { monthlyRevenue: 0, monthlyExpenses: 0, profit: 0 };
      try {
        const salesReport = await window.electronAPI.reports.getSalesByPeriod('month');
        const expensesReport = await window.electronAPI.reports.getExpensesByPeriod('month');
        
        financialData = {
          monthlyRevenue: salesReport?.total || 0,
          monthlyExpenses: Math.abs(expensesReport?.total || 0),
          profit: (salesReport?.total || 0) - Math.abs(expensesReport?.total || 0)
        };
      } catch (error) {
        console.warn('خطأ في تحميل البيانات المالية:', error);
      }

      // تحميل بيانات العمال
      const workers = await window.electronAPI.workers.getAll();
      const activeWorkers = workers.filter(w => w.status !== 'غير نشط');

      // حساب عبء العمل
      let totalWorkload = 0;
      for (const worker of activeWorkers) {
        try {
          const assignments = await window.electronAPI.orderWorkers.getByWorkerId(worker.id);
          const activeAssignments = assignments.filter(a => a.status === 'قيد التنفيذ');
          totalWorkload += activeAssignments.length;
        } catch (error) {
          console.warn('خطأ في تحميل مهام العامل:', worker.name);
        }
      }

      const avgWorkload = activeWorkers.length > 0 ? totalWorkload / activeWorkers.length : 0;

      // تحميل التنبيهات
      const allNotifications = await window.electronAPI.notifications.getAll();
      const unreadNotifications = allNotifications.filter(n => !n.read);

      // تحديث البيانات
      setDashboardData({
        orders: ordersStats,
        materials: {
          lowStock: lowStockMaterials.length,
          totalValue: inventory.reduce((sum, item) => sum + (item.quantity * item.cost_per_unit || 0), 0),
          reserved: totalReserved
        },
        production: productionData,
        quality: qualityData,
        financial: financialData,
        workers: {
          active: activeWorkers.length,
          efficiency: productionData.efficiency,
          workload: Math.round(avgWorkload)
        }
      });

      setRecentOrders(orders.slice(0, 5));
      setNotifications(unreadNotifications.slice(0, 5));

      // إنشاء بيانات الرسم البياني للإنتاج
      const last7Days = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        return date.toISOString().split('T')[0];
      }).reverse();

      const chartData = last7Days.map(date => ({
        date,
        completed: orders.filter(o => 
          o.status === 'مكتمل' && 
          o.updated_at?.startsWith(date)
        ).length,
        started: orders.filter(o => 
          o.status === 'قيد التنفيذ' && 
          o.created_at?.startsWith(date)
        ).length
      }));

      setProductionChart(chartData);

    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'dashboard-loading' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل لوحة التحكم...')
    );
  }

  return React.createElement('div', { className: 'advanced-dashboard' },
    // رأس لوحة التحكم
    React.createElement('div', { className: 'dashboard-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'dashboard-title' },
          React.createElement('i', { className: 'fas fa-tachometer-alt' }),
          ' لوحة التحكم - نظام التصنيع حسب الطلب'
        ),
        React.createElement('div', { className: 'header-info' },
          React.createElement('div', { className: 'current-time' },
            new Date().toLocaleString('ar-SA', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })
          ),
          React.createElement('button', { 
            className: 'refresh-btn',
            onClick: loadDashboardData,
            title: 'تحديث البيانات'
          },
            React.createElement('i', { className: 'fas fa-sync-alt' })
          )
        )
      )
    ),

    // مؤشرات الأداء الرئيسية
    React.createElement('div', { className: 'advanced-kpi-grid' },
      // مؤشر الطلبات المخصصة
      React.createElement('div', { className: 'advanced-kpi-card orders-kpi' },
        React.createElement('div', { className: 'kpi-header' },
          React.createElement('div', { className: 'kpi-icon' },
            React.createElement('i', { className: 'fas fa-hammer' })
          ),
          React.createElement('h3', null, 'الطلبات المخصصة'),
          React.createElement('div', { className: 'kpi-trend positive' },
            React.createElement('i', { className: 'fas fa-arrow-up' }),
            ' +12%'
          )
        ),
        React.createElement('div', { className: 'kpi-content' },
          React.createElement('div', { className: 'main-value' }, dashboardData.orders.total),
          React.createElement('div', { className: 'kpi-breakdown' },
            React.createElement('div', { className: 'breakdown-item' },
              React.createElement('span', { className: 'label' }, 'جديد'),
              React.createElement('span', { className: 'value pending' }, dashboardData.orders.pending)
            ),
            React.createElement('div', { className: 'breakdown-item' },
              React.createElement('span', { className: 'label' }, 'قيد التنفيذ'),
              React.createElement('span', { className: 'value progress' }, dashboardData.orders.inProgress)
            ),
            React.createElement('div', { className: 'breakdown-item' },
              React.createElement('span', { className: 'label' }, 'مكتمل'),
              React.createElement('span', { className: 'value completed' }, dashboardData.orders.completed)
            )
          )
        )
      ),

      // مؤشر المواد الخام
      React.createElement('div', { className: 'advanced-kpi-card materials-kpi' },
        React.createElement('div', { className: 'kpi-header' },
          React.createElement('div', { className: 'kpi-icon' },
            React.createElement('i', { className: 'fas fa-boxes' })
          ),
          React.createElement('h3', null, 'المواد الخام'),
          React.createElement('div', { className: 'kpi-alert' },
            React.createElement('i', { className: 'fas fa-exclamation-triangle' }),
            ` ${dashboardData.materials.lowStock} منخفض`
          )
        ),
        React.createElement('div', { className: 'kpi-content' },
          React.createElement('div', { className: 'main-value' }, `${(dashboardData.materials.totalValue / 1000).toFixed(1)}k د.ل`),
          React.createElement('div', { className: 'kpi-breakdown' },
            React.createElement('div', { className: 'breakdown-item' },
              React.createElement('span', { className: 'label' }, 'محجوز'),
              React.createElement('span', { className: 'value reserved' }, dashboardData.materials.reserved)
            ),
            React.createElement('div', { className: 'breakdown-item' },
              React.createElement('span', { className: 'label' }, 'منخفض المخزون'),
              React.createElement('span', { className: 'value warning' }, dashboardData.materials.lowStock)
            )
          )
        )
      ),

      // مؤشر الإنتاج
      React.createElement('div', { className: 'advanced-kpi-card production-kpi' },
        React.createElement('div', { className: 'kpi-header' },
          React.createElement('div', { className: 'kpi-icon' },
            React.createElement('i', { className: 'fas fa-cogs' })
          ),
          React.createElement('h3', null, 'الإنتاج'),
          React.createElement('div', { className: 'kpi-trend positive' },
            React.createElement('i', { className: 'fas fa-chart-line' }),
            ` ${dashboardData.production.efficiency}%`
          )
        ),
        React.createElement('div', { className: 'kpi-content' },
          React.createElement('div', { className: 'main-value' }, `${dashboardData.production.completionRate}%`),
          React.createElement('div', { className: 'kpi-breakdown' },
            React.createElement('div', { className: 'breakdown-item' },
              React.createElement('span', { className: 'label' }, 'مراحل نشطة'),
              React.createElement('span', { className: 'value active' }, dashboardData.production.activeStages)
            ),
            React.createElement('div', { className: 'breakdown-item' },
              React.createElement('span', { className: 'label' }, 'الكفاءة'),
              React.createElement('span', { className: 'value efficiency' }, `${dashboardData.production.efficiency}%`)
            )
          )
        )
      )
    )
  );
};

module.exports = Dashboard;
