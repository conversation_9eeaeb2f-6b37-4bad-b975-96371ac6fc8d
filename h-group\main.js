const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('better-sqlite3');

// دوال التنسيق
const formatCurrency = (amount) => {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return '0 د.ل';
  }
  const formattedNumber = Math.round(amount).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return `${formattedNumber} د.ل`;
};

// إنشاء متجر الإعدادات البديل
const store = {
  data: {},
  get: function(key) { return this.data[key]; },
  set: function(key, value) { this.data[key] = value; },
  store: this.data
};
// const PDFDocument = require('pdfkit'); // تم تعطيل مؤقتاً
// const QRCode = require('qrcode'); // تم تعطيل مؤقتاً
// const ExcelJS = require('exceljs'); // تم تعطيل مؤقتاً

// استيراد electron-store بطريقة CommonJS (معطل مؤقتاً)
// const Store = require('electron-store');





// متغير لتخزين النافذة الرئيسية
let mainWindow;

// إنشاء قاعدة البيانات أو الاتصال بها إذا كانت موجودة
let db;

function createDatabase() {
  try {
    console.log('بدء إنشاء قاعدة البيانات...');
    console.log('مسار قاعدة البيانات:', path.join(app.getPath('userData'), 'hgroup.db'));

    // التحقق من وجود مجلد البيانات
    const userDataPath = app.getPath('userData');
    if (!fs.existsSync(userDataPath)) {
      console.log('إنشاء مجلد البيانات:', userDataPath);
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    // محاولة إنشاء قاعدة البيانات
    try {
      db = new sqlite3(path.join(userDataPath, 'hgroup.db'), { verbose: console.log });
      console.log('تم فتح قاعدة البيانات بنجاح');
    } catch (dbError) {
      console.error('خطأ في فتح قاعدة البيانات:', dbError);
      dialog.showErrorBox('خطأ في قاعدة البيانات', `فشل فتح قاعدة البيانات: ${dbError.message}`);
      return false;
    }

    // إنشاء جداول قاعدة البيانات إذا لم تكن موجودة

    // جدول العملاء
    db.exec(`
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        address TEXT,
        email TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المنتجات
    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT,
        default_price REAL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المواد الخام
    db.exec(`
      CREATE TABLE IF NOT EXISTS materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        unit TEXT,
        cost_per_unit REAL DEFAULT 0,
        min_quantity REAL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المخزون
    db.exec(`
      CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        material_id INTEGER,
        quantity REAL DEFAULT 0,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (material_id) REFERENCES materials (id)
      )
    `);

    // جدول الطلبات المخصصة
    db.exec(`
      CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE,
        customer_id INTEGER,
        order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        delivery_date TIMESTAMP,

        -- معلومات المنتج المخصص
        product_name TEXT NOT NULL,
        product_category TEXT,
        dimensions TEXT, -- الأبعاد (طول × عرض × ارتفاع)
        wood_type TEXT, -- نوع الخشب
        finish_type TEXT, -- نوع التشطيب
        color TEXT, -- اللون
        hardware_details TEXT, -- تفاصيل الإكسسوارات
        custom_specifications TEXT, -- مواصفات إضافية مخصصة

        -- معلومات الإنتاج
        estimated_hours REAL DEFAULT 0, -- الساعات المقدرة للإنتاج
        actual_hours REAL DEFAULT 0, -- الساعات الفعلية
        complexity_level TEXT DEFAULT 'متوسط', -- (بسيط، متوسط، معقد، معقد جداً)
        priority_level TEXT DEFAULT 'عادي', -- (عادي، عالي، عاجل)

        -- التكاليف والأسعار
        status TEXT DEFAULT 'جديد',
        worker_fee REAL DEFAULT 0,
        factory_fee REAL DEFAULT 0,
        designer_fee REAL DEFAULT 0,
        owner_margin REAL DEFAULT 0,
        materials_cost REAL DEFAULT 0,
        total_cost REAL DEFAULT 0,
        final_price REAL DEFAULT 0,

        -- ملاحظات
        design_notes TEXT, -- ملاحظات التصميم
        production_notes TEXT, -- ملاحظات الإنتاج
        customer_notes TEXT, -- ملاحظات العميل
        internal_notes TEXT, -- ملاحظات داخلية

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    `);

    // جدول المواد الخام المستخدمة في الطلبات مع نظام الحجز
    db.exec(`
      CREATE TABLE IF NOT EXISTS order_materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER,
        material_id INTEGER,
        quantity REAL,
        reserved_quantity REAL DEFAULT 0, -- الكمية المحجوزة
        cost_per_unit REAL,
        total_cost REAL,
        waste_percentage REAL DEFAULT 5, -- نسبة الهدر المتوقعة
        actual_quantity_used REAL, -- الكمية المستخدمة فعلياً
        reservation_status TEXT DEFAULT 'محجوزة', -- (محجوزة، مستخدمة، ملغاة)
        reserved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        used_at TIMESTAMP,
        notes TEXT,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (material_id) REFERENCES materials (id)
      )
    `);

    // جدول حجوزات المواد
    db.exec(`
      CREATE TABLE IF NOT EXISTS material_reservations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        material_id INTEGER NOT NULL,
        order_id INTEGER NOT NULL,
        reserved_quantity REAL NOT NULL,
        status TEXT DEFAULT 'نشط', -- (نشط، مستخدم، ملغي، منتهي الصلاحية)
        reserved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP, -- تاريخ انتهاء الحجز
        used_at TIMESTAMP,
        cancelled_at TIMESTAMP,
        notes TEXT,
        FOREIGN KEY (material_id) REFERENCES materials (id),
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `);

    // جدول مراحل الإنتاج للطلبات المخصصة
    db.exec(`
      CREATE TABLE IF NOT EXISTS production_stages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        stage_name TEXT NOT NULL, -- (تصميم، قطع الخشب، تجميع، تشطيب، تركيب الإكسسوارات، فحص الجودة، تسليم)
        stage_order INTEGER NOT NULL, -- ترتيب المرحلة
        description TEXT, -- وصف المرحلة
        estimated_hours REAL DEFAULT 0, -- الساعات المقدرة
        actual_hours REAL DEFAULT 0, -- الساعات الفعلية
        estimated_cost REAL DEFAULT 0, -- التكلفة المقدرة
        actual_cost REAL DEFAULT 0, -- التكلفة الفعلية
        assigned_worker_id INTEGER, -- العامل المكلف
        start_date TIMESTAMP, -- تاريخ البدء
        end_date TIMESTAMP, -- تاريخ الانتهاء
        planned_start_date TIMESTAMP, -- تاريخ البدء المخطط
        planned_end_date TIMESTAMP, -- تاريخ الانتهاء المخطط
        status TEXT DEFAULT 'لم تبدأ', -- (لم تبدأ، قيد التنفيذ، مكتملة، متأخرة، معلقة)
        completion_percentage REAL DEFAULT 0, -- نسبة الإنجاز
        quality_check_passed BOOLEAN DEFAULT 0, -- اجتياز فحص الجودة
        notes TEXT, -- ملاحظات المرحلة
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (assigned_worker_id) REFERENCES workers (id)
      )
    `);

    // جدول المواصفات التفصيلية المخصصة
    db.exec(`
      CREATE TABLE IF NOT EXISTS custom_specifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        spec_category TEXT NOT NULL, -- (أبعاد، مواد، تشطيب، إكسسوارات، تصميم، وظائف)
        spec_name TEXT NOT NULL, -- اسم المواصفة
        spec_value TEXT NOT NULL, -- قيمة المواصفة
        spec_unit TEXT, -- وحدة القياس إن وجدت
        is_critical BOOLEAN DEFAULT 0, -- هل المواصفة حرجة
        affects_cost BOOLEAN DEFAULT 0, -- هل تؤثر على التكلفة
        cost_impact REAL DEFAULT 0, -- تأثير التكلفة
        spec_notes TEXT, -- ملاحظات المواصفة
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `);

    // جدول نقاط فحص الجودة
    db.exec(`
      CREATE TABLE IF NOT EXISTS quality_checkpoints (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        stage_id INTEGER NOT NULL,
        checkpoint_name TEXT NOT NULL,
        description TEXT,
        is_mandatory BOOLEAN DEFAULT 1,
        check_order INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (stage_id) REFERENCES production_stages (id)
      )
    `);

    // جدول فحوصات الجودة
    db.exec(`
      CREATE TABLE IF NOT EXISTS quality_checks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        checkpoint_id INTEGER NOT NULL,
        stage_id INTEGER NOT NULL,
        order_id INTEGER NOT NULL,
        inspector_id INTEGER, -- العامل المسؤول عن الفحص
        check_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'قيد الفحص', -- (قيد الفحص، مقبول، مرفوض، يحتاج إصلاح)
        quality_score INTEGER, -- درجة الجودة من 1-10
        defects_found TEXT, -- العيوب المكتشفة
        corrective_actions TEXT, -- الإجراءات التصحيحية
        images TEXT, -- مسارات الصور
        notes TEXT,
        approved_by INTEGER, -- من وافق على الفحص
        approved_at TIMESTAMP,
        FOREIGN KEY (checkpoint_id) REFERENCES quality_checkpoints (id),
        FOREIGN KEY (stage_id) REFERENCES production_stages (id),
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (inspector_id) REFERENCES workers (id),
        FOREIGN KEY (approved_by) REFERENCES workers (id)
      )
    `);

    // جدول العيوب والإصلاحات
    db.exec(`
      CREATE TABLE IF NOT EXISTS defects_repairs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        stage_id INTEGER,
        quality_check_id INTEGER,
        defect_type TEXT NOT NULL, -- نوع العيب
        defect_description TEXT NOT NULL,
        severity TEXT DEFAULT 'متوسط', -- (بسيط، متوسط، خطير، حرج)
        detected_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        detected_by INTEGER, -- من اكتشف العيب
        repair_description TEXT,
        repair_cost REAL DEFAULT 0,
        repair_time_hours REAL DEFAULT 0,
        repaired_by INTEGER, -- من قام بالإصلاح
        repair_date TIMESTAMP,
        status TEXT DEFAULT 'مكتشف', -- (مكتشف، قيد الإصلاح، مصلح، غير قابل للإصلاح)
        verification_status TEXT DEFAULT 'لم يتم', -- (لم يتم، تم التحقق، فشل التحقق)
        verified_by INTEGER,
        verified_at TIMESTAMP,
        notes TEXT,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (stage_id) REFERENCES production_stages (id),
        FOREIGN KEY (quality_check_id) REFERENCES quality_checks (id),
        FOREIGN KEY (detected_by) REFERENCES workers (id),
        FOREIGN KEY (repaired_by) REFERENCES workers (id),
        FOREIGN KEY (verified_by) REFERENCES workers (id)
      )
    `);

    // جدول العاملين المحسن
    db.exec(`
      CREATE TABLE IF NOT EXISTS workers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_number TEXT UNIQUE, -- الرقم الوظيفي
        name TEXT NOT NULL,
        role TEXT, -- (نجار، دهان، مجمع، مصمم، إلخ)
        specialization TEXT, -- التخصص الدقيق
        phone TEXT,
        address TEXT,
        national_id TEXT, -- رقم الهوية

        -- نوع الأجر
        payment_type TEXT DEFAULT 'per_meter', -- (per_meter, fixed_salary, per_piece, hourly)
        rate_per_meter REAL DEFAULT 0, -- السعر بالمتر المربع
        fixed_salary REAL DEFAULT 0, -- الراتب الثابت الشهري
        rate_per_hour REAL DEFAULT 0, -- السعر بالساعة

        -- معلومات الحساب البنكي
        bank_name TEXT,
        account_number TEXT,
        iban TEXT,

        -- الحالة الوظيفية
        employment_status TEXT DEFAULT 'active', -- (active, inactive, suspended, terminated)
        hire_date DATE,
        termination_date DATE,

        -- إعدادات الأجر
        overtime_rate REAL DEFAULT 1.5, -- معدل الوقت الإضافي
        bonus_eligible BOOLEAN DEFAULT 1, -- مؤهل للمكافآت
        insurance_deduction REAL DEFAULT 0, -- خصم التأمين
        tax_deduction_percentage REAL DEFAULT 0, -- نسبة خصم الضريبة

        -- للتوافق مع النظام القديم
        fee_per_order REAL DEFAULT 0,
        monthly_salary REAL DEFAULT 0,

        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول تعيين العمال للطلبات المحسن
    db.exec(`
      CREATE TABLE IF NOT EXISTS order_workers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER,
        worker_id INTEGER,
        assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        -- تفاصيل العمل والأجر
        work_area_meters REAL DEFAULT 0, -- المساحة بالمتر المربع
        rate_per_meter REAL DEFAULT 0, -- السعر المتفق عليه بالمتر
        total_amount REAL DEFAULT 0, -- المبلغ الإجمالي المستحق
        actual_hours REAL DEFAULT 0, -- الساعات الفعلية
        overtime_hours REAL DEFAULT 0, -- ساعات إضافية

        -- حالة العمل والدفع
        work_status TEXT DEFAULT 'قيد التنفيذ', -- (قيد التنفيذ، مكتمل، متوقف، ملغي)
        payment_status TEXT DEFAULT 'غير مدفوع', -- (غير مدفوع، مدفوع جزئياً، مدفوع كاملاً)
        completion_date TIMESTAMP,

        -- للتوافق مع النظام القديم
        fee REAL DEFAULT 0,
        status TEXT DEFAULT 'قيد التنفيذ',

        notes TEXT,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (worker_id) REFERENCES workers (id)
      )
    `);

    // جدول السلف والدفعات المقدمة
    db.exec(`
      CREATE TABLE IF NOT EXISTS worker_advances (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        worker_id INTEGER NOT NULL,
        advance_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        amount REAL NOT NULL,
        reason TEXT, -- سبب السلفة
        repayment_method TEXT DEFAULT 'monthly_deduction', -- (monthly_deduction, lump_sum, installments)
        monthly_deduction_amount REAL DEFAULT 0, -- مبلغ الخصم الشهري
        remaining_balance REAL, -- الرصيد المتبقي
        status TEXT DEFAULT 'active', -- (active, paid_off, cancelled)
        approved_by TEXT, -- من وافق على السلفة
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (worker_id) REFERENCES workers (id)
      )
    `);

    // جدول الخصومات والغرامات
    db.exec(`
      CREATE TABLE IF NOT EXISTS worker_deductions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        worker_id INTEGER NOT NULL,
        deduction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deduction_type TEXT NOT NULL, -- (absence, damage, late, quality_issue, advance_repayment, insurance, tax)
        amount REAL NOT NULL,
        description TEXT,
        related_order_id INTEGER, -- إذا كان الخصم مرتبط بطلب معين
        status TEXT DEFAULT 'active', -- (active, cancelled, refunded)
        approved_by TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (worker_id) REFERENCES workers (id),
        FOREIGN KEY (related_order_id) REFERENCES orders (id)
      )
    `);

    // جدول المكافآت والحوافز
    db.exec(`
      CREATE TABLE IF NOT EXISTS worker_bonuses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        worker_id INTEGER NOT NULL,
        bonus_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        bonus_type TEXT NOT NULL, -- (quality_bonus, speed_bonus, monthly_bonus, annual_bonus, project_completion)
        amount REAL NOT NULL,
        description TEXT,
        related_order_id INTEGER, -- إذا كانت المكافأة مرتبطة بطلب معين
        approved_by TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (worker_id) REFERENCES workers (id),
        FOREIGN KEY (related_order_id) REFERENCES orders (id)
      )
    `);

    // جدول كشوف المرتبات الشهرية
    db.exec(`
      CREATE TABLE IF NOT EXISTS monthly_payrolls (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        worker_id INTEGER NOT NULL,
        payroll_month INTEGER NOT NULL, -- الشهر (1-12)
        payroll_year INTEGER NOT NULL, -- السنة

        -- تفاصيل الأجر
        total_meters_worked REAL DEFAULT 0, -- إجمالي الأمتار المنجزة
        total_hours_worked REAL DEFAULT 0, -- إجمالي الساعات
        overtime_hours REAL DEFAULT 0, -- الساعات الإضافية

        -- الحسابات المالية
        base_salary REAL DEFAULT 0, -- الراتب الأساسي
        meter_earnings REAL DEFAULT 0, -- أجر الأمتار
        overtime_earnings REAL DEFAULT 0, -- أجر الوقت الإضافي
        bonuses_total REAL DEFAULT 0, -- إجمالي المكافآت
        gross_salary REAL DEFAULT 0, -- الراتب الإجمالي

        -- الخصومات
        advances_deduction REAL DEFAULT 0, -- خصم السلف
        other_deductions REAL DEFAULT 0, -- خصومات أخرى
        insurance_deduction REAL DEFAULT 0, -- خصم التأمين
        tax_deduction REAL DEFAULT 0, -- خصم الضريبة
        total_deductions REAL DEFAULT 0, -- إجمالي الخصومات

        -- الصافي
        net_salary REAL DEFAULT 0, -- الراتب الصافي

        -- حالة الدفع
        payment_status TEXT DEFAULT 'pending', -- (pending, paid, partially_paid)
        payment_date TIMESTAMP,
        payment_method TEXT, -- (cash, bank_transfer, check)

        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (worker_id) REFERENCES workers (id),
        UNIQUE(worker_id, payroll_month, payroll_year)
      )
    `);

    // جدول دفعات المرتبات
    db.exec(`
      CREATE TABLE IF NOT EXISTS payroll_payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        payroll_id INTEGER NOT NULL,
        worker_id INTEGER NOT NULL,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        amount REAL NOT NULL,
        payment_method TEXT, -- (cash, bank_transfer, check)
        reference_number TEXT, -- رقم المرجع أو الشيك
        notes TEXT,
        created_by TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (payroll_id) REFERENCES monthly_payrolls (id),
        FOREIGN KEY (worker_id) REFERENCES workers (id)
      )
    `);

    // جدول المصنع والتكاليف التشغيلية
    db.exec(`
      CREATE TABLE IF NOT EXISTS factory_expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        expense_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        category TEXT,
        description TEXT,
        amount REAL,
        recurring BOOLEAN DEFAULT 0,
        period TEXT
      )
    `);

    // جدول الفواتير
    db.exec(`
      CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE,
        order_id INTEGER,
        issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        due_date TIMESTAMP,
        total_amount REAL,
        paid_amount REAL DEFAULT 0,
        status TEXT DEFAULT 'غير مدفوعة',
        payment_type TEXT DEFAULT 'كامل', /* كامل، أقساط */
        installments_count INTEGER DEFAULT 1,
        notes TEXT,
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `);

    // جدول المدفوعات
    db.exec(`
      CREATE TABLE IF NOT EXISTS payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        amount REAL,
        payment_method TEXT,
        notes TEXT,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id)
      )
    `);

    // جدول الأقساط
    db.exec(`
      CREATE TABLE IF NOT EXISTS installments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER,
        installment_number INTEGER,
        amount REAL,
        due_date TIMESTAMP,
        payment_date TIMESTAMP,
        status TEXT DEFAULT 'غير مدفوع', /* غير مدفوع، مدفوع، متأخر */
        payment_id INTEGER,
        notes TEXT,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id),
        FOREIGN KEY (payment_id) REFERENCES payments (id)
      )
    `);

    // جدول العمليات المالية
    db.exec(`
      CREATE TABLE IF NOT EXISTS financial_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        type TEXT,
        category TEXT,
        amount REAL,
        description TEXT,
        related_order_id INTEGER,
        related_worker_id INTEGER,
        related_invoice_id INTEGER,
        related_expense_id INTEGER,
        FOREIGN KEY (related_order_id) REFERENCES orders (id),
        FOREIGN KEY (related_worker_id) REFERENCES workers (id),
        FOREIGN KEY (related_invoice_id) REFERENCES invoices (id),
        FOREIGN KEY (related_expense_id) REFERENCES factory_expenses (id)
      )
    `);

    // جدول المستخدمين
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT,
        role TEXT DEFAULT 'user',
        permissions TEXT,
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول سجل العمليات
    db.exec(`
      CREATE TABLE IF NOT EXISTS activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action_type TEXT,
        action_details TEXT,
        action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // جدول الإشعارات
    db.exec(`
      CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        link TEXT,
        read BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
    if (userCount.count === 0) {
      db.prepare('INSERT INTO users (username, password, full_name, role, permissions) VALUES (?, ?, ?, ?, ?)')
        .run('admin', 'admin123', 'مدير النظام', 'admin', 'all');
    }

    console.log('تم إنشاء قاعدة البيانات بنجاح');
    return true;
  } catch (error) {
    console.error('خطأ في إنشاء قاعدة البيانات:', error);
    return false;
  }
}

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false
    },
    icon: path.join(__dirname, 'assets/icons/icon.png')
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile('index.html');

  // فتح أدوات المطور في بيئة التطوير
  if (process.env.NODE_ENV === 'development' || process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // التعامل مع إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // إضافة معالج للأخطاء
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('فشل تحميل الصفحة:', errorCode, errorDescription);

    // إعادة تحميل الصفحة في حالة فشل التحميل
    if (mainWindow) {
      mainWindow.loadFile('index.html');
    }
  });
}

// عند جاهزية التطبيق
app.whenReady().then(() => {
  try {
    console.log('بدء تشغيل التطبيق...');

    // إنشاء النافذة الرئيسية أولاً
    createWindow();

    // إنشاء قاعدة البيانات
    const dbCreated = createDatabase();

    if (dbCreated) {
      console.log('تم إنشاء قاعدة البيانات بنجاح');

      // إنشاء مستخدم افتراضي إذا لم يكن موجوداً
      try {
        const userExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE username = ?').get('admin');

        if (!userExists || userExists.count === 0) {
          console.log('إنشاء مستخدم افتراضي...');

          // إنشاء مستخدم افتراضي
          db.prepare(`
            INSERT INTO users (username, password, full_name, role, permissions)
            VALUES (?, ?, ?, ?, ?)
          `).run('admin', 'admin123', 'مدير النظام', 'admin', 'all');

          console.log('تم إنشاء مستخدم افتراضي بنجاح');
        }

        // تسجيل دخول المستخدم الافتراضي تلقائياً
        const user = db.prepare('SELECT * FROM users WHERE username = ?').get('admin');
        if (user) {
          currentUser = {
            id: user.id,
            username: user.username,
            full_name: user.full_name,
            role: user.role,
            permissions: user.permissions
          };
          console.log('تم تسجيل دخول المستخدم الافتراضي تلقائياً');
        }
      } catch (userError) {
        console.error('خطأ في إنشاء المستخدم الافتراضي:', userError);
      }
    } else {
      console.error('فشل إنشاء قاعدة البيانات');
      dialog.showErrorBox('خطأ في قاعدة البيانات', 'فشل إنشاء قاعدة البيانات. قد تواجه مشاكل في استخدام التطبيق.');
    }
  } catch (error) {
    console.error('خطأ أثناء بدء التطبيق:', error);
    dialog.showErrorBox('خطأ في بدء التطبيق', `حدث خطأ أثناء بدء التطبيق: ${error.message}`);
    app.quit();
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// إغلاق التطبيق عند إغلاق جميع النوافذ (ماعدا في macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// إغلاق قاعدة البيانات عند إغلاق التطبيق
app.on('will-quit', () => {
  if (db) {
    db.close();
  }
});

// استقبال طلبات IPC من الواجهة
// معالجات الأحداث للعملاء
ipcMain.handle('customers:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM customers ORDER BY name').all();
  } catch (error) {
    console.error('خطأ في الحصول على العملاء:', error);
    throw error;
  }
});

ipcMain.handle('customers:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM customers WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على العميل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('customers:create', async (event, customer) => {
  try {
    const result = db.prepare(
      'INSERT INTO customers (name, phone, address, email, notes) VALUES (?, ?, ?, ?, ?)'
    ).run(customer.name, customer.phone, customer.address, customer.email, customer.notes);

    return { id: result.lastInsertRowid, ...customer };
  } catch (error) {
    console.error('خطأ في إنشاء عميل جديد:', error);
    throw error;
  }
});

ipcMain.handle('customers:update', async (event, id, customer) => {
  try {
    db.prepare(
      'UPDATE customers SET name = ?, phone = ?, address = ?, email = ?, notes = ? WHERE id = ?'
    ).run(customer.name, customer.phone, customer.address, customer.email, customer.notes, id);

    return { id, ...customer };
  } catch (error) {
    console.error(`خطأ في تحديث العميل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('customers:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM customers WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف العميل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('customers:search', async (event, query) => {
  try {
    return db.prepare(
      'SELECT * FROM customers WHERE name LIKE ? OR phone LIKE ? OR address LIKE ? OR email LIKE ? ORDER BY name'
    ).all(`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن العملاء بالاستعلام "${query}":`, error);
    throw error;
  }
});



// معالجات الأحداث للمواد الخام
ipcMain.handle('materials:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM materials ORDER BY name').all();
  } catch (error) {
    console.error('خطأ في الحصول على المواد الخام:', error);
    throw error;
  }
});

ipcMain.handle('materials:getAllWithInventory', async () => {
  try {
    return db.prepare(`
      SELECT m.*, i.quantity, i.last_updated
      FROM materials m
      LEFT JOIN inventory i ON m.id = i.material_id
      ORDER BY m.name
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المواد الخام مع المخزون:', error);
    throw error;
  }
});

ipcMain.handle('materials:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM materials WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على المادة الخام رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('materials:create', async (event, material) => {
  try {
    const result = db.prepare(
      'INSERT INTO materials (name, description, unit, cost_per_unit, min_quantity) VALUES (?, ?, ?, ?, ?)'
    ).run(material.name, material.description, material.unit, material.cost_per_unit, material.min_quantity);

    // إنشاء سجل مخزون أولي للمادة الخام
    db.prepare('INSERT INTO inventory (material_id, quantity) VALUES (?, 0)').run(result.lastInsertRowid);

    return { id: result.lastInsertRowid, ...material };
  } catch (error) {
    console.error('خطأ في إنشاء مادة خام جديدة:', error);
    throw error;
  }
});

ipcMain.handle('materials:update', async (event, id, material) => {
  try {
    db.prepare(
      'UPDATE materials SET name = ?, description = ?, unit = ?, cost_per_unit = ?, min_quantity = ? WHERE id = ?'
    ).run(material.name, material.description, material.unit, material.cost_per_unit, material.min_quantity, id);

    return { id, ...material };
  } catch (error) {
    console.error(`خطأ في تحديث المادة الخام رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('materials:delete', async (event, id) => {
  try {
    // حذف سجل المخزون المرتبط بالمادة الخام
    db.prepare('DELETE FROM inventory WHERE material_id = ?').run(id);

    // حذف المادة الخام
    db.prepare('DELETE FROM materials WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف المادة الخام رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('materials:search', async (event, query) => {
  try {
    return db.prepare(
      'SELECT * FROM materials WHERE name LIKE ? OR description LIKE ? OR unit LIKE ? ORDER BY name'
    ).all(`%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن المواد الخام بالاستعلام "${query}":`, error);
    throw error;
  }
});

// معالجات الأحداث للمخزون
ipcMain.handle('inventory:getAll', async () => {
  try {
    return db.prepare(`
      SELECT i.*, m.name as material_name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      ORDER BY m.name
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المخزون:', error);
    throw error;
  }
});

ipcMain.handle('inventory:getByMaterialId', async (event, materialId) => {
  try {
    return db.prepare(`
      SELECT i.*, m.name as material_name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      WHERE i.material_id = ?
    `).get(materialId);
  } catch (error) {
    console.error(`خطأ في الحصول على مخزون المادة الخام رقم ${materialId}:`, error);
    throw error;
  }
});

ipcMain.handle('inventory:update', async (event, materialId, quantity) => {
  try {
    db.prepare(
      'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
    ).run(quantity, materialId);

    return { success: true, materialId, quantity };
  } catch (error) {
    console.error(`خطأ في تحديث مخزون المادة الخام رقم ${materialId}:`, error);
    throw error;
  }
});

ipcMain.handle('inventory:getLowStock', async () => {
  try {
    return db.prepare(`
      SELECT i.*, m.name as material_name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      WHERE i.quantity <= m.min_quantity
      ORDER BY m.name
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المواد منخفضة المخزون:', error);
    throw error;
  }
});

ipcMain.handle('inventory:getLowInventoryItems', async () => {
  try {
    return db.prepare(`
      SELECT i.*, m.name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      WHERE i.quantity <= m.min_quantity
      ORDER BY m.name
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المواد ذات المخزون المنخفض:', error);
    throw error;
  }
});

// معالجات الأحداث للطلبات
ipcMain.handle('orders:getAll', async () => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      ORDER BY o.order_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الطلبات:', error);
    throw error;
  }
});

ipcMain.handle('orders:getById', async (event, id) => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE o.id = ?
    `).get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على الطلب رقم ${id}:`, error);
    throw error;
  }
});

// إنشاء طلب مخصص جديد
ipcMain.handle('orders:createCustom', async (event, order) => {
  try {
    // بدء المعاملة
    db.prepare('BEGIN TRANSACTION').run();

    // إنشاء رقم طلب فريد
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    const lastOrder = db.prepare(`
      SELECT order_number FROM orders
      WHERE order_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`ORD-${year}${month}%`);

    let sequenceNumber = 1;
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.order_number.split('-')[2]);
      sequenceNumber = lastSequence + 1;
    }

    const orderNumber = `ORD-${year}${month}-${sequenceNumber.toString().padStart(3, '0')}`;

    // إدخال الطلب المخصص الجديد
    const result = db.prepare(`
      INSERT INTO orders (
        order_number, customer_id, order_date, delivery_date,
        product_name, product_category, dimensions, wood_type, finish_type, color,
        hardware_details, custom_specifications, estimated_hours, complexity_level, priority_level,
        status, worker_fee, factory_fee, designer_fee, owner_margin, materials_cost, total_cost, final_price,
        design_notes, production_notes, customer_notes, internal_notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      orderNumber,
      order.customer_id,
      order.order_date || new Date().toISOString(),
      order.delivery_date,
      order.product_name,
      order.product_category,
      order.dimensions,
      order.wood_type,
      order.finish_type,
      order.color,
      order.hardware_details,
      order.custom_specifications,
      order.estimated_hours || 0,
      order.complexity_level || 'متوسط',
      order.priority_level || 'عادي',
      order.status || 'جديد',
      order.worker_fee || 0,
      order.factory_fee || 0,
      order.designer_fee || 0,
      order.owner_margin || 0,
      order.materials_cost || 0,
      order.total_cost || 0,
      order.final_price || 0,
      order.design_notes,
      order.production_notes,
      order.customer_notes,
      order.internal_notes
    );

    const orderId = result.lastInsertRowid;

    // إضافة المواد الخام
    if (order.materials && order.materials.length > 0) {
      for (const material of order.materials) {
        if (material.material_id && material.quantity > 0) {
          db.prepare(`
            INSERT INTO order_materials (
              order_id, material_id, quantity, cost_per_unit, total_cost, waste_percentage, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            orderId,
            material.material_id,
            material.quantity,
            material.cost_per_unit,
            material.total_cost,
            material.waste_percentage || 5,
            material.notes
          );
        }
      }
    }

    // إضافة المواصفات المخصصة
    if (order.customSpecs && order.customSpecs.length > 0) {
      for (const spec of order.customSpecs) {
        if (spec.spec_name && spec.spec_value) {
          db.prepare(`
            INSERT INTO custom_specifications (
              order_id, spec_category, spec_name, spec_value, spec_unit,
              is_critical, affects_cost, cost_impact, spec_notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            orderId,
            spec.spec_category,
            spec.spec_name,
            spec.spec_value,
            spec.spec_unit,
            spec.is_critical ? 1 : 0,
            spec.affects_cost ? 1 : 0,
            spec.cost_impact || 0,
            spec.spec_notes
          );
        }
      }
    }

    // إنشاء مراحل الإنتاج الافتراضية
    const defaultStages = [
      { name: 'تصميم وتخطيط', order: 1, description: 'إعداد التصميم والرسوم التقنية' },
      { name: 'قطع الخشب', order: 2, description: 'قطع وتحضير قطع الخشب حسب المقاسات' },
      { name: 'تجميع أولي', order: 3, description: 'تجميع القطع الأساسية' },
      { name: 'تشطيب ودهان', order: 4, description: 'عمليات التشطيب والدهان' },
      { name: 'تركيب الإكسسوارات', order: 5, description: 'تركيب المفصلات والمقابض' },
      { name: 'فحص الجودة', order: 6, description: 'فحص نهائي للجودة' },
      { name: 'التسليم', order: 7, description: 'تحضير المنتج للتسليم' }
    ];

    for (const stage of defaultStages) {
      db.prepare(`
        INSERT INTO production_stages (
          order_id, stage_name, stage_order, description, estimated_hours, status
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        orderId,
        stage.name,
        stage.order,
        stage.description,
        Math.ceil((order.estimated_hours || 0) / defaultStages.length),
        'لم تبدأ'
      );
    }

    // إتمام المعاملة
    db.prepare('COMMIT').run();

    return { id: orderId, order_number: orderNumber, ...order };
  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    db.prepare('ROLLBACK').run();
    console.error('خطأ في إنشاء طلب مخصص جديد:', error);
    throw error;
  }
});

// إنشاء طلب عادي (للتوافق مع النظام القديم)
ipcMain.handle('orders:create', async (event, order) => {
  try {
    // إنشاء رقم طلب فريد
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    // الحصول على آخر رقم تسلسلي للطلبات في الشهر الحالي
    const lastOrder = db.prepare(`
      SELECT order_number FROM orders
      WHERE order_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`ORD-${year}${month}%`);

    let sequenceNumber = 1;
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.order_number.split('-')[2]);
      sequenceNumber = lastSequence + 1;
    }

    const orderNumber = `ORD-${year}${month}-${sequenceNumber.toString().padStart(3, '0')}`;

    // إدخال الطلب الجديد
    const result = db.prepare(`
      INSERT INTO orders (
        order_number, customer_id, product_id, order_date, delivery_date,
        specifications, status, worker_fee, factory_fee, designer_fee,
        owner_margin, materials_cost, final_price, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      orderNumber,
      order.customer_id,
      order.product_id,
      order.order_date || new Date().toISOString(),
      order.delivery_date,
      order.specifications,
      order.status || 'قيد التنفيذ',
      order.worker_fee || 0,
      order.factory_fee || 0,
      order.designer_fee || 0,
      order.owner_margin || 0,
      order.materials_cost || 0,
      order.final_price || 0,
      order.notes
    );

    return {
      id: result.lastInsertRowid,
      order_number: orderNumber,
      ...order
    };
  } catch (error) {
    console.error('خطأ في إنشاء طلب جديد:', error);
    throw error;
  }
});

ipcMain.handle('orders:update', async (event, id, order) => {
  try {
    db.prepare(`
      UPDATE orders SET
        customer_id = ?, product_id = ?, delivery_date = ?,
        specifications = ?, status = ?, worker_fee = ?,
        factory_fee = ?, designer_fee = ?, owner_margin = ?,
        materials_cost = ?, final_price = ?, notes = ?
      WHERE id = ?
    `).run(
      order.customer_id,
      order.product_id,
      order.delivery_date,
      order.specifications,
      order.status,
      order.worker_fee,
      order.factory_fee,
      order.designer_fee,
      order.owner_margin,
      order.materials_cost,
      order.final_price,
      order.notes,
      id
    );

    return { id, ...order };
  } catch (error) {
    console.error(`خطأ في تحديث الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orders:delete', async (event, id) => {
  try {
    // حذف المواد المستخدمة في الطلب
    db.prepare('DELETE FROM order_materials WHERE order_id = ?').run(id);

    // حذف العمال المعينين للطلب
    db.prepare('DELETE FROM order_workers WHERE order_id = ?').run(id);

    // حذف الطلب
    db.prepare('DELETE FROM orders WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orders:search', async (event, query) => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE o.order_number LIKE ?
        OR c.name LIKE ?
        OR p.name LIKE ?
        OR o.specifications LIKE ?
      ORDER BY o.order_date DESC
    `).all(`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن الطلبات بالاستعلام "${query}":`, error);
    throw error;
  }
});

ipcMain.handle('orders:getByStatus', async (event, status) => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE o.status = ?
      ORDER BY o.order_date DESC
    `).all(status);
  } catch (error) {
    console.error(`خطأ في الحصول على الطلبات بحالة "${status}":`, error);
    throw error;
  }
});

ipcMain.handle('orders:updateStatus', async (event, id, status) => {
  try {
    db.prepare('UPDATE orders SET status = ? WHERE id = ?').run(status, id);
    return { success: true, id, status };
  } catch (error) {
    console.error(`خطأ في تحديث حالة الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orders:getLateOrders', async () => {
  try {
    const today = new Date().toISOString().split('T')[0];

    return db.prepare(`
      SELECT o.*, c.name as customer_name, c.phone as customer_phone
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      WHERE o.status = 'قيد التنفيذ'
        AND o.delivery_date < ?
        AND o.delivery_date IS NOT NULL
      ORDER BY o.delivery_date
    `).all(today + 'T00:00:00.000Z');
  } catch (error) {
    console.error('خطأ في الحصول على الطلبات المتأخرة:', error);
    throw error;
  }
});

ipcMain.handle('orders:calculatePrice', async (event, orderData) => {
  try {
    // حساب تكلفة المواد
    let materialsCost = 0;
    if (orderData.materials && orderData.materials.length > 0) {
      for (const material of orderData.materials) {
        materialsCost += material.quantity * material.cost_per_unit;
      }
    }

    // حساب السعر النهائي
    const workerFee = orderData.worker_fee || 0;
    const factoryFee = orderData.factory_fee || 0;
    const designerFee = orderData.designer_fee || 0;
    const ownerMargin = orderData.owner_margin || 0;

    const totalCost = materialsCost + workerFee + factoryFee + designerFee;
    const finalPrice = totalCost + ownerMargin;

    return {
      materials_cost: materialsCost,
      total_cost: totalCost,
      final_price: finalPrice
    };
  } catch (error) {
    console.error('خطأ في حساب سعر الطلب:', error);
    throw error;
  }
});

// التنبؤ بتكلفة الطلب قبل إنشائه
ipcMain.handle('orders:predictCost', async (event, productId, specifications) => {
  try {
    // الحصول على معلومات المنتج
    let product = null;
    if (productId) {
      product = db.prepare('SELECT * FROM products WHERE id = ?').get(productId);
    }

    // البحث عن طلبات مشابهة
    let similarOrders = [];

    if (product) {
      // البحث عن طلبات لنفس المنتج
      similarOrders = db.prepare(`
        SELECT o.*, COUNT(om.id) as materials_count, SUM(om.total_cost) as materials_cost
        FROM orders o
        LEFT JOIN order_materials om ON o.id = om.order_id
        WHERE o.product_id = ?
        GROUP BY o.id
        ORDER BY o.order_date DESC
        LIMIT 5
      `).all(productId);
    } else if (specifications) {
      // البحث عن طلبات بمواصفات مشابهة
      similarOrders = db.prepare(`
        SELECT o.*, COUNT(om.id) as materials_count, SUM(om.total_cost) as materials_cost
        FROM orders o
        LEFT JOIN order_materials om ON o.id = om.order_id
        WHERE o.specifications LIKE ?
        GROUP BY o.id
        ORDER BY o.order_date DESC
        LIMIT 5
      `).all(`%${specifications}%`);
    }

    // إذا لم يتم العثور على طلبات مشابهة، استخدم متوسط جميع الطلبات
    if (similarOrders.length === 0) {
      similarOrders = db.prepare(`
        SELECT o.*, COUNT(om.id) as materials_count, SUM(om.total_cost) as materials_cost
        FROM orders o
        LEFT JOIN order_materials om ON o.id = om.order_id
        GROUP BY o.id
        ORDER BY o.order_date DESC
        LIMIT 10
      `).all();
    }

    // حساب متوسط التكاليف من الطلبات المشابهة
    let avgMaterialsCost = 0;
    let avgWorkerFee = 0;
    let avgFactoryFee = 0;
    let avgDesignerFee = 0;
    let avgOwnerMargin = 0;
    let avgTotalCost = 0;
    let avgFinalPrice = 0;

    if (similarOrders.length > 0) {
      // حساب المتوسطات
      for (const order of similarOrders) {
        avgMaterialsCost += order.materials_cost || 0;
        avgWorkerFee += order.worker_fee || 0;
        avgFactoryFee += order.factory_fee || 0;
        avgDesignerFee += order.designer_fee || 0;
        avgOwnerMargin += order.owner_margin || 0;
        avgTotalCost += (order.materials_cost || 0) + (order.worker_fee || 0) + (order.factory_fee || 0) + (order.designer_fee || 0);
        avgFinalPrice += order.final_price || 0;
      }

      avgMaterialsCost /= similarOrders.length;
      avgWorkerFee /= similarOrders.length;
      avgFactoryFee /= similarOrders.length;
      avgDesignerFee /= similarOrders.length;
      avgOwnerMargin /= similarOrders.length;
      avgTotalCost /= similarOrders.length;
      avgFinalPrice /= similarOrders.length;
    }

    // الحصول على الإعدادات الافتراضية
    const defaultWorkerFee = store.get('defaultWorkerFee') || 0;
    const defaultFactoryFee = store.get('defaultFactoryFee') || 0;
    const defaultDesignerFee = store.get('defaultDesignerFee') || 0;
    const defaultOwnerMargin = store.get('defaultOwnerMargin') || 0;

    // استخدام القيم الافتراضية إذا لم يتم العثور على طلبات مشابهة
    if (similarOrders.length === 0) {
      avgWorkerFee = defaultWorkerFee;
      avgFactoryFee = defaultFactoryFee;
      avgDesignerFee = defaultDesignerFee;
      avgOwnerMargin = defaultOwnerMargin;
    }

    // الحصول على المواد المستخدمة في الطلبات المشابهة
    let suggestedMaterials = [];

    if (similarOrders.length > 0) {
      // الحصول على المواد الأكثر استخداماً في الطلبات المشابهة
      suggestedMaterials = db.prepare(`
        SELECT m.id, m.name, m.unit, m.cost_per_unit,
               AVG(om.quantity) as avg_quantity,
               COUNT(om.id) as usage_count
        FROM order_materials om
        JOIN materials m ON om.material_id = m.id
        WHERE om.order_id IN (${similarOrders.map(o => o.id).join(',')})
        GROUP BY m.id
        ORDER BY usage_count DESC, avg_quantity DESC
      `).all();
    }

    return {
      predicted_costs: {
        materials_cost: avgMaterialsCost,
        worker_fee: avgWorkerFee || defaultWorkerFee,
        factory_fee: avgFactoryFee || defaultFactoryFee,
        designer_fee: avgDesignerFee || defaultDesignerFee,
        owner_margin: avgOwnerMargin || defaultOwnerMargin,
        total_cost: avgTotalCost,
        final_price: avgFinalPrice
      },
      suggested_materials: suggestedMaterials,
      similar_orders_count: similarOrders.length,
      product: product
    };
  } catch (error) {
    console.error('خطأ في التنبؤ بتكلفة الطلب:', error);
    throw error;
  }
});

// معالجات الأحداث لمراحل الإنتاج
ipcMain.handle('productionStages:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT ps.*, w.name as worker_name, w.role as worker_role
      FROM production_stages ps
      LEFT JOIN workers w ON ps.assigned_worker_id = w.id
      WHERE ps.order_id = ?
      ORDER BY ps.stage_order
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على مراحل الإنتاج للطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('productionStages:create', async (event, stage) => {
  try {
    const result = db.prepare(`
      INSERT INTO production_stages (
        order_id, stage_name, stage_order, description, estimated_hours,
        estimated_cost, assigned_worker_id, planned_start_date, planned_end_date, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      stage.order_id,
      stage.stage_name,
      stage.stage_order,
      stage.description,
      stage.estimated_hours || 0,
      stage.estimated_cost || 0,
      stage.assigned_worker_id,
      stage.planned_start_date,
      stage.planned_end_date,
      stage.notes
    );

    return { id: result.lastInsertRowid, ...stage };
  } catch (error) {
    console.error('خطأ في إنشاء مرحلة إنتاج جديدة:', error);
    throw error;
  }
});

ipcMain.handle('productionStages:update', async (event, id, stage) => {
  try {
    db.prepare(`
      UPDATE production_stages SET
        stage_name = ?, description = ?, estimated_hours = ?, actual_hours = ?,
        estimated_cost = ?, actual_cost = ?, assigned_worker_id = ?,
        start_date = ?, end_date = ?, planned_start_date = ?, planned_end_date = ?,
        status = ?, completion_percentage = ?, quality_check_passed = ?,
        notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(
      stage.stage_name,
      stage.description,
      stage.estimated_hours,
      stage.actual_hours,
      stage.estimated_cost,
      stage.actual_cost,
      stage.assigned_worker_id,
      stage.start_date,
      stage.end_date,
      stage.planned_start_date,
      stage.planned_end_date,
      stage.status,
      stage.completion_percentage,
      stage.quality_check_passed ? 1 : 0,
      stage.notes,
      id
    );

    return { id, ...stage };
  } catch (error) {
    console.error(`خطأ في تحديث مرحلة الإنتاج رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('productionStages:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM production_stages WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف مرحلة الإنتاج رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('productionStages:updateStatus', async (event, id, status, completionPercentage) => {
  try {
    const updateData = {
      status,
      completion_percentage: completionPercentage || 0,
      updated_at: new Date().toISOString()
    };

    // إذا بدأت المرحلة، تسجيل تاريخ البدء
    if (status === 'قيد التنفيذ') {
      updateData.start_date = new Date().toISOString();
    }

    // إذا انتهت المرحلة، تسجيل تاريخ الانتهاء
    if (status === 'مكتملة') {
      updateData.end_date = new Date().toISOString();
      updateData.completion_percentage = 100;
    }

    const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(updateData), id];

    db.prepare(`UPDATE production_stages SET ${setClause} WHERE id = ?`).run(...values);

    return { success: true, id, status, completion_percentage: updateData.completion_percentage };
  } catch (error) {
    console.error(`خطأ في تحديث حالة مرحلة الإنتاج رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث للمواصفات المخصصة
ipcMain.handle('customSpecifications:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT * FROM custom_specifications
      WHERE order_id = ?
      ORDER BY spec_category, spec_name
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على المواصفات المخصصة للطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('customSpecifications:create', async (event, specification) => {
  try {
    const result = db.prepare(`
      INSERT INTO custom_specifications (
        order_id, spec_category, spec_name, spec_value, spec_unit,
        is_critical, affects_cost, cost_impact, spec_notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      specification.order_id,
      specification.spec_category,
      specification.spec_name,
      specification.spec_value,
      specification.spec_unit,
      specification.is_critical ? 1 : 0,
      specification.affects_cost ? 1 : 0,
      specification.cost_impact || 0,
      specification.spec_notes
    );

    return { id: result.lastInsertRowid, ...specification };
  } catch (error) {
    console.error('خطأ في إنشاء مواصفة مخصصة جديدة:', error);
    throw error;
  }
});

ipcMain.handle('customSpecifications:update', async (event, id, specification) => {
  try {
    db.prepare(`
      UPDATE custom_specifications SET
        spec_category = ?, spec_name = ?, spec_value = ?, spec_unit = ?,
        is_critical = ?, affects_cost = ?, cost_impact = ?, spec_notes = ?
      WHERE id = ?
    `).run(
      specification.spec_category,
      specification.spec_name,
      specification.spec_value,
      specification.spec_unit,
      specification.is_critical ? 1 : 0,
      specification.affects_cost ? 1 : 0,
      specification.cost_impact || 0,
      specification.spec_notes,
      id
    );

    return { id, ...specification };
  } catch (error) {
    console.error(`خطأ في تحديث المواصفة المخصصة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('customSpecifications:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM custom_specifications WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف المواصفة المخصصة رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث لحجز المواد
ipcMain.handle('materialReservations:create', async (event, reservation) => {
  try {
    // التحقق من توفر الكمية المطلوبة
    const inventory = db.prepare('SELECT quantity FROM inventory WHERE material_id = ?').get(reservation.material_id);
    if (!inventory || inventory.quantity < reservation.reserved_quantity) {
      throw new Error('الكمية المطلوبة غير متوفرة في المخزون');
    }

    // التحقق من الحجوزات الحالية
    const currentReservations = db.prepare(`
      SELECT SUM(reserved_quantity) as total_reserved
      FROM material_reservations
      WHERE material_id = ? AND status = 'نشط'
    `).get(reservation.material_id);

    const availableQuantity = inventory.quantity - (currentReservations.total_reserved || 0);
    if (availableQuantity < reservation.reserved_quantity) {
      throw new Error(`الكمية المتاحة للحجز: ${availableQuantity}`);
    }

    // إنشاء الحجز
    const result = db.prepare(`
      INSERT INTO material_reservations (
        material_id, order_id, reserved_quantity, expires_at, notes
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      reservation.material_id,
      reservation.order_id,
      reservation.reserved_quantity,
      reservation.expires_at,
      reservation.notes
    );

    return { id: result.lastInsertRowid, ...reservation };
  } catch (error) {
    console.error('خطأ في إنشاء حجز المادة:', error);
    throw error;
  }
});

ipcMain.handle('materialReservations:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT mr.*, m.name as material_name, m.unit
      FROM material_reservations mr
      JOIN materials m ON mr.material_id = m.id
      WHERE mr.order_id = ?
      ORDER BY mr.reserved_at DESC
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على حجوزات الطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('materialReservations:getByMaterialId', async (event, materialId) => {
  try {
    return db.prepare(`
      SELECT mr.*, o.order_number, c.name as customer_name
      FROM material_reservations mr
      JOIN orders o ON mr.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE mr.material_id = ? AND mr.status = 'نشط'
      ORDER BY mr.reserved_at DESC
    `).all(materialId);
  } catch (error) {
    console.error(`خطأ في الحصول على حجوزات المادة رقم ${materialId}:`, error);
    throw error;
  }
});

ipcMain.handle('materialReservations:use', async (event, reservationId, usedQuantity) => {
  try {
    db.prepare('BEGIN TRANSACTION').run();

    const reservation = db.prepare('SELECT * FROM material_reservations WHERE id = ?').get(reservationId);
    if (!reservation) {
      throw new Error('الحجز غير موجود');
    }

    if (usedQuantity > reservation.reserved_quantity) {
      throw new Error('الكمية المستخدمة أكبر من الكمية المحجوزة');
    }

    // تحديث حالة الحجز
    db.prepare(`
      UPDATE material_reservations SET
        status = 'مستخدم',
        used_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(reservationId);

    // تحديث المخزون
    db.prepare(`
      UPDATE inventory SET
        quantity = quantity - ?,
        last_updated = CURRENT_TIMESTAMP
      WHERE material_id = ?
    `).run(usedQuantity, reservation.material_id);

    // إضافة سجل في order_materials
    db.prepare(`
      UPDATE order_materials SET
        actual_quantity_used = ?,
        used_at = CURRENT_TIMESTAMP,
        reservation_status = 'مستخدمة'
      WHERE order_id = ? AND material_id = ?
    `).run(usedQuantity, reservation.order_id, reservation.material_id);

    db.prepare('COMMIT').run();
    return { success: true, reservationId, usedQuantity };
  } catch (error) {
    db.prepare('ROLLBACK').run();
    console.error('خطأ في استخدام الحجز:', error);
    throw error;
  }
});

ipcMain.handle('materialReservations:cancel', async (event, reservationId) => {
  try {
    db.prepare(`
      UPDATE material_reservations SET
        status = 'ملغي',
        cancelled_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(reservationId);

    return { success: true, reservationId };
  } catch (error) {
    console.error('خطأ في إلغاء الحجز:', error);
    throw error;
  }
});

// معالجات الأحداث للمواد المستخدمة في الطلبات
ipcMain.handle('orderMaterials:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT om.*, m.name as material_name, m.unit,
             (SELECT quantity FROM inventory WHERE material_id = om.material_id) as available_quantity,
             (SELECT SUM(reserved_quantity) FROM material_reservations
              WHERE material_id = om.material_id AND status = 'نشط') as reserved_quantity
      FROM order_materials om
      JOIN materials m ON om.material_id = m.id
      WHERE om.order_id = ?
      ORDER BY m.name
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على مواد الطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('orderMaterials:add', async (event, orderMaterial) => {
  try {
    // حساب التكلفة الإجمالية
    const totalCost = orderMaterial.quantity * orderMaterial.cost_per_unit;

    const result = db.prepare(`
      INSERT INTO order_materials (order_id, material_id, quantity, cost_per_unit, total_cost)
      VALUES (?, ?, ?, ?, ?)
    `).run(
      orderMaterial.order_id,
      orderMaterial.material_id,
      orderMaterial.quantity,
      orderMaterial.cost_per_unit,
      totalCost
    );

    // تحديث مخزون المادة الخام
    const inventory = db.prepare('SELECT quantity FROM inventory WHERE material_id = ?').get(orderMaterial.material_id);
    if (inventory) {
      const newQuantity = Math.max(0, inventory.quantity - orderMaterial.quantity);
      db.prepare(
        'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
      ).run(newQuantity, orderMaterial.material_id);
    }

    // تحديث تكلفة المواد في الطلب
    const orderMaterials = db.prepare('SELECT SUM(total_cost) as total FROM order_materials WHERE order_id = ?').get(orderMaterial.order_id);
    if (orderMaterials && orderMaterials.total) {
      db.prepare('UPDATE orders SET materials_cost = ? WHERE id = ?').run(orderMaterials.total, orderMaterial.order_id);
    }

    return {
      id: result.lastInsertRowid,
      ...orderMaterial,
      total_cost: totalCost
    };
  } catch (error) {
    console.error('خطأ في إضافة مادة للطلب:', error);
    throw error;
  }
});

ipcMain.handle('orderMaterials:update', async (event, id, orderMaterial) => {
  try {
    // الحصول على الكمية السابقة
    const oldOrderMaterial = db.prepare('SELECT * FROM order_materials WHERE id = ?').get(id);

    // حساب التكلفة الإجمالية الجديدة
    const totalCost = orderMaterial.quantity * orderMaterial.cost_per_unit;

    db.prepare(`
      UPDATE order_materials
      SET material_id = ?, quantity = ?, cost_per_unit = ?, total_cost = ?
      WHERE id = ?
    `).run(
      orderMaterial.material_id,
      orderMaterial.quantity,
      orderMaterial.cost_per_unit,
      totalCost,
      id
    );

    // تحديث مخزون المادة الخام
    if (oldOrderMaterial) {
      const quantityDiff = orderMaterial.quantity - oldOrderMaterial.quantity;

      if (quantityDiff !== 0) {
        const inventory = db.prepare('SELECT quantity FROM inventory WHERE material_id = ?').get(orderMaterial.material_id);
        if (inventory) {
          const newQuantity = Math.max(0, inventory.quantity - quantityDiff);
          db.prepare(
            'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
          ).run(newQuantity, orderMaterial.material_id);
        }
      }
    }

    // تحديث تكلفة المواد في الطلب
    const orderMaterials = db.prepare('SELECT SUM(total_cost) as total FROM order_materials WHERE order_id = ?').get(orderMaterial.order_id);
    if (orderMaterials && orderMaterials.total) {
      db.prepare('UPDATE orders SET materials_cost = ? WHERE id = ?').run(orderMaterials.total, orderMaterial.order_id);
    }

    return {
      id,
      ...orderMaterial,
      total_cost: totalCost
    };
  } catch (error) {
    console.error(`خطأ في تحديث مادة الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orderMaterials:delete', async (event, id) => {
  try {
    // الحصول على معلومات المادة قبل الحذف
    const orderMaterial = db.prepare('SELECT * FROM order_materials WHERE id = ?').get(id);

    if (orderMaterial) {
      // حذف المادة من الطلب
      db.prepare('DELETE FROM order_materials WHERE id = ?').run(id);

      // إعادة الكمية إلى المخزون
      const inventory = db.prepare('SELECT quantity FROM inventory WHERE material_id = ?').get(orderMaterial.material_id);
      if (inventory) {
        const newQuantity = inventory.quantity + orderMaterial.quantity;
        db.prepare(
          'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
        ).run(newQuantity, orderMaterial.material_id);
      }

      // تحديث تكلفة المواد في الطلب
      const orderMaterials = db.prepare('SELECT SUM(total_cost) as total FROM order_materials WHERE order_id = ?').get(orderMaterial.order_id);
      const totalCost = orderMaterials && orderMaterials.total ? orderMaterials.total : 0;
      db.prepare('UPDATE orders SET materials_cost = ? WHERE id = ?').run(totalCost, orderMaterial.order_id);
    }

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف مادة الطلب رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث للعمال
ipcMain.handle('workers:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM workers ORDER BY name').all();
  } catch (error) {
    console.error('خطأ في الحصول على العمال:', error);
    throw error;
  }
});

// معالجات السلف والدفعات المقدمة
ipcMain.handle('workerAdvances:getAll', async () => {
  try {
    return db.prepare(`
      SELECT wa.*, w.name as worker_name, w.employee_number
      FROM worker_advances wa
      JOIN workers w ON wa.worker_id = w.id
      ORDER BY wa.advance_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على السلف:', error);
    throw error;
  }
});

ipcMain.handle('workerAdvances:getByWorkerId', async (event, workerId) => {
  try {
    return db.prepare(`
      SELECT * FROM worker_advances
      WHERE worker_id = ?
      ORDER BY advance_date DESC
    `).all(workerId);
  } catch (error) {
    console.error(`خطأ في الحصول على سلف العامل رقم ${workerId}:`, error);
    throw error;
  }
});

ipcMain.handle('workerAdvances:create', async (event, advance) => {
  try {
    const result = db.prepare(`
      INSERT INTO worker_advances (
        worker_id, amount, reason, repayment_method,
        monthly_deduction_amount, remaining_balance, approved_by, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      advance.worker_id,
      advance.amount,
      advance.reason,
      advance.repayment_method || 'monthly_deduction',
      advance.monthly_deduction_amount || 0,
      advance.amount, // الرصيد المتبقي يساوي المبلغ في البداية
      advance.approved_by,
      advance.notes
    );

    return { id: result.lastInsertRowid, ...advance };
  } catch (error) {
    console.error('خطأ في إنشاء سلفة جديدة:', error);
    throw error;
  }
});

ipcMain.handle('workerAdvances:update', async (event, id, advance) => {
  try {
    db.prepare(`
      UPDATE worker_advances SET
        amount = ?, reason = ?, repayment_method = ?,
        monthly_deduction_amount = ?, remaining_balance = ?,
        status = ?, notes = ?
      WHERE id = ?
    `).run(
      advance.amount,
      advance.reason,
      advance.repayment_method,
      advance.monthly_deduction_amount,
      advance.remaining_balance,
      advance.status,
      advance.notes,
      id
    );

    return { id, ...advance };
  } catch (error) {
    console.error(`خطأ في تحديث السلفة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workerAdvances:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM worker_advances WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف السلفة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workers:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM workers WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workers:create', async (event, worker) => {
  try {
    const result = db.prepare(`
      INSERT INTO workers (
        employee_number, name, role, specialization, phone, address, national_id,
        payment_type, rate_per_meter, fixed_salary, rate_per_hour,
        bank_name, account_number, iban,
        employment_status, hire_date,
        overtime_rate, bonus_eligible, insurance_deduction, tax_deduction_percentage,
        fee_per_order, monthly_salary, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      worker.employee_number,
      worker.name,
      worker.role,
      worker.specialization,
      worker.phone,
      worker.address,
      worker.national_id,
      worker.payment_type || 'per_meter',
      worker.rate_per_meter || 0,
      worker.fixed_salary || 0,
      worker.rate_per_hour || 0,
      worker.bank_name,
      worker.account_number,
      worker.iban,
      worker.employment_status || 'active',
      worker.hire_date,
      worker.overtime_rate || 1.5,
      worker.bonus_eligible !== false ? 1 : 0,
      worker.insurance_deduction || 0,
      worker.tax_deduction_percentage || 0,
      worker.fee_per_order || 0,
      worker.monthly_salary || 0,
      worker.notes
    );

    return { id: result.lastInsertRowid, ...worker };
  } catch (error) {
    console.error('خطأ في إنشاء عامل جديد:', error);
    throw error;
  }
});

ipcMain.handle('workers:update', async (event, id, worker) => {
  try {
    db.prepare(`
      UPDATE workers SET
        employee_number = ?, name = ?, role = ?, specialization = ?,
        phone = ?, address = ?, national_id = ?,
        payment_type = ?, rate_per_meter = ?, fixed_salary = ?, rate_per_hour = ?,
        bank_name = ?, account_number = ?, iban = ?,
        employment_status = ?, hire_date = ?, termination_date = ?,
        overtime_rate = ?, bonus_eligible = ?, insurance_deduction = ?, tax_deduction_percentage = ?,
        fee_per_order = ?, monthly_salary = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(
      worker.employee_number,
      worker.name,
      worker.role,
      worker.specialization,
      worker.phone,
      worker.address,
      worker.national_id,
      worker.payment_type || 'per_meter',
      worker.rate_per_meter || 0,
      worker.fixed_salary || 0,
      worker.rate_per_hour || 0,
      worker.bank_name,
      worker.account_number,
      worker.iban,
      worker.employment_status || 'active',
      worker.hire_date,
      worker.termination_date,
      worker.overtime_rate || 1.5,
      worker.bonus_eligible !== false ? 1 : 0,
      worker.insurance_deduction || 0,
      worker.tax_deduction_percentage || 0,
      worker.fee_per_order || 0,
      worker.monthly_salary || 0,
      worker.notes,
      id
    );

    return { id, ...worker };
  } catch (error) {
    console.error(`خطأ في تحديث العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workers:delete', async (event, id) => {
  try {
    // التحقق من عدم وجود طلبات مرتبطة بالعامل
    const orderWorkers = db.prepare('SELECT COUNT(*) as count FROM order_workers WHERE worker_id = ?').get(id);
    if (orderWorkers && orderWorkers.count > 0) {
      throw new Error('لا يمكن حذف العامل لأنه مرتبط بطلبات');
    }

    db.prepare('DELETE FROM workers WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workers:search', async (event, query) => {
  try {
    return db.prepare(
      'SELECT * FROM workers WHERE name LIKE ? OR role LIKE ? OR phone LIKE ? OR address LIKE ? ORDER BY name'
    ).all(`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن العمال بالاستعلام "${query}":`, error);
    throw error;
  }
});

ipcMain.handle('workers:getByRole', async (event, role) => {
  try {
    return db.prepare('SELECT * FROM workers WHERE role = ? ORDER BY name').all(role);
  } catch (error) {
    console.error(`خطأ في الحصول على العمال بدور "${role}":`, error);
    throw error;
  }
});

// معالجات الخصومات
ipcMain.handle('workerDeductions:getAll', async () => {
  try {
    return db.prepare(`
      SELECT wd.*, w.name as worker_name, w.employee_number,
             o.order_number
      FROM worker_deductions wd
      JOIN workers w ON wd.worker_id = w.id
      LEFT JOIN orders o ON wd.related_order_id = o.id
      ORDER BY wd.deduction_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الخصومات:', error);
    throw error;
  }
});

ipcMain.handle('workerDeductions:getByWorkerId', async (event, workerId) => {
  try {
    return db.prepare(`
      SELECT wd.*, o.order_number
      FROM worker_deductions wd
      LEFT JOIN orders o ON wd.related_order_id = o.id
      WHERE wd.worker_id = ?
      ORDER BY wd.deduction_date DESC
    `).all(workerId);
  } catch (error) {
    console.error(`خطأ في الحصول على خصومات العامل رقم ${workerId}:`, error);
    throw error;
  }
});

ipcMain.handle('workerDeductions:create', async (event, deduction) => {
  try {
    const result = db.prepare(`
      INSERT INTO worker_deductions (
        worker_id, deduction_type, amount, description,
        related_order_id, approved_by, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(
      deduction.worker_id,
      deduction.deduction_type,
      deduction.amount,
      deduction.description,
      deduction.related_order_id,
      deduction.approved_by,
      deduction.notes
    );

    return { id: result.lastInsertRowid, ...deduction };
  } catch (error) {
    console.error('خطأ في إنشاء خصم جديد:', error);
    throw error;
  }
});

// معالجات المكافآت
ipcMain.handle('workerBonuses:getAll', async () => {
  try {
    return db.prepare(`
      SELECT wb.*, w.name as worker_name, w.employee_number,
             o.order_number
      FROM worker_bonuses wb
      JOIN workers w ON wb.worker_id = w.id
      LEFT JOIN orders o ON wb.related_order_id = o.id
      ORDER BY wb.bonus_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المكافآت:', error);
    throw error;
  }
});

ipcMain.handle('workerBonuses:getByWorkerId', async (event, workerId) => {
  try {
    return db.prepare(`
      SELECT wb.*, o.order_number
      FROM worker_bonuses wb
      LEFT JOIN orders o ON wb.related_order_id = o.id
      WHERE wb.worker_id = ?
      ORDER BY wb.bonus_date DESC
    `).all(workerId);
  } catch (error) {
    console.error(`خطأ في الحصول على مكافآت العامل رقم ${workerId}:`, error);
    throw error;
  }
});

ipcMain.handle('workerBonuses:create', async (event, bonus) => {
  try {
    const result = db.prepare(`
      INSERT INTO worker_bonuses (
        worker_id, bonus_type, amount, description,
        related_order_id, approved_by, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(
      bonus.worker_id,
      bonus.bonus_type,
      bonus.amount,
      bonus.description,
      bonus.related_order_id,
      bonus.approved_by,
      bonus.notes
    );

    return { id: result.lastInsertRowid, ...bonus };
  } catch (error) {
    console.error('خطأ في إنشاء مكافأة جديدة:', error);
    throw error;
  }
});

// معالجات الأحداث لتعيين العمال للطلبات
ipcMain.handle('orderWorkers:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT ow.*, w.name as worker_name, w.role as worker_role
      FROM order_workers ow
      JOIN workers w ON ow.worker_id = w.id
      WHERE ow.order_id = ?
      ORDER BY ow.assigned_date
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على العمال المعينين للطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:getByWorkerId', async (event, workerId) => {
  try {
    return db.prepare(`
      SELECT ow.*, o.order_number, o.specifications, o.status as order_status
      FROM order_workers ow
      JOIN orders o ON ow.order_id = o.id
      WHERE ow.worker_id = ?
      ORDER BY ow.assigned_date DESC
    `).all(workerId);
  } catch (error) {
    console.error(`خطأ في الحصول على الطلبات المعينة للعامل رقم ${workerId}:`, error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:assign', async (event, orderWorker) => {
  try {
    const result = db.prepare(
      'INSERT INTO order_workers (order_id, worker_id, assigned_date, fee, status) VALUES (?, ?, ?, ?, ?)'
    ).run(
      orderWorker.order_id,
      orderWorker.worker_id,
      orderWorker.assigned_date || new Date().toISOString(),
      orderWorker.fee || 0,
      orderWorker.status || 'قيد التنفيذ'
    );

    // تحديث أجرة العامل في الطلب إذا كان الأول
    const worker = db.prepare('SELECT fee_per_order FROM workers WHERE id = ?').get(orderWorker.worker_id);
    if (worker) {
      const orderWorkersCount = db.prepare('SELECT COUNT(*) as count FROM order_workers WHERE order_id = ?').get(orderWorker.order_id);
      if (orderWorkersCount && orderWorkersCount.count === 1) {
        db.prepare('UPDATE orders SET worker_fee = ? WHERE id = ?').run(worker.fee_per_order, orderWorker.order_id);
      }
    }

    return { id: result.lastInsertRowid, ...orderWorker };
  } catch (error) {
    console.error('خطأ في تعيين عامل للطلب:', error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:update', async (event, id, orderWorker) => {
  try {
    db.prepare(
      'UPDATE order_workers SET worker_id = ?, assigned_date = ?, fee = ?, status = ? WHERE id = ?'
    ).run(
      orderWorker.worker_id,
      orderWorker.assigned_date,
      orderWorker.fee,
      orderWorker.status,
      id
    );

    return { id, ...orderWorker };
  } catch (error) {
    console.error(`خطأ في تحديث تعيين العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM order_workers WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف تعيين العامل رقم ${id}:`, error);
    throw error;
  }
});

// معالجات كشوف المرتبات الشهرية
ipcMain.handle('monthlyPayrolls:getAll', async () => {
  try {
    return db.prepare(`
      SELECT mp.*, w.name as worker_name, w.employee_number
      FROM monthly_payrolls mp
      JOIN workers w ON mp.worker_id = w.id
      ORDER BY mp.payroll_year DESC, mp.payroll_month DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على كشوف المرتبات:', error);
    throw error;
  }
});

ipcMain.handle('monthlyPayrolls:getByWorkerId', async (event, workerId) => {
  try {
    return db.prepare(`
      SELECT * FROM monthly_payrolls
      WHERE worker_id = ?
      ORDER BY payroll_year DESC, payroll_month DESC
    `).all(workerId);
  } catch (error) {
    console.error(`خطأ في الحصول على كشوف مرتبات العامل رقم ${workerId}:`, error);
    throw error;
  }
});

ipcMain.handle('monthlyPayrolls:getByPeriod', async (event, month, year) => {
  try {
    return db.prepare(`
      SELECT mp.*, w.name as worker_name, w.employee_number
      FROM monthly_payrolls mp
      JOIN workers w ON mp.worker_id = w.id
      WHERE mp.payroll_month = ? AND mp.payroll_year = ?
      ORDER BY w.name
    `).all(month, year);
  } catch (error) {
    console.error(`خطأ في الحصول على كشوف مرتبات الشهر ${month}/${year}:`, error);
    throw error;
  }
});

ipcMain.handle('monthlyPayrolls:calculateForWorker', async (event, workerId, month, year) => {
  try {
    // الحصول على بيانات العامل
    const worker = db.prepare('SELECT * FROM workers WHERE id = ?').get(workerId);
    if (!worker) {
      throw new Error('العامل غير موجود');
    }

    // حساب الأمتار المنجزة في الشهر
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
    const endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`;

    const workData = db.prepare(`
      SELECT
        SUM(work_area_meters) as total_meters,
        SUM(actual_hours) as total_hours,
        SUM(overtime_hours) as overtime_hours,
        SUM(total_amount) as total_earnings
      FROM order_workers
      WHERE worker_id = ?
        AND completion_date >= ?
        AND completion_date < ?
        AND work_status = 'مكتمل'
    `).get(workerId, startDate, endDate);

    // حساب المكافآت
    const bonuses = db.prepare(`
      SELECT SUM(amount) as total_bonuses
      FROM worker_bonuses
      WHERE worker_id = ?
        AND bonus_date >= ?
        AND bonus_date < ?
    `).get(workerId, startDate, endDate);

    // حساب الخصومات
    const deductions = db.prepare(`
      SELECT SUM(amount) as total_deductions
      FROM worker_deductions
      WHERE worker_id = ?
        AND deduction_date >= ?
        AND deduction_date < ?
        AND status = 'active'
    `).get(workerId, startDate, endDate);

    // حساب خصم السلف
    const advanceDeduction = db.prepare(`
      SELECT SUM(monthly_deduction_amount) as advance_deduction
      FROM worker_advances
      WHERE worker_id = ?
        AND status = 'active'
        AND remaining_balance > 0
    `).get(workerId);

    // الحسابات
    const totalMeters = workData?.total_meters || 0;
    const totalHours = workData?.total_hours || 0;
    const overtimeHours = workData?.overtime_hours || 0;

    const baseSalary = worker.fixed_salary || 0;
    const meterEarnings = totalMeters * (worker.rate_per_meter || 0);
    const overtimeEarnings = overtimeHours * (worker.rate_per_hour || 0) * (worker.overtime_rate || 1.5);
    const bonusesTotal = bonuses?.total_bonuses || 0;

    const grossSalary = baseSalary + meterEarnings + overtimeEarnings + bonusesTotal;

    const advancesDeduction = advanceDeduction?.advance_deduction || 0;
    const otherDeductions = deductions?.total_deductions || 0;
    const insuranceDeduction = worker.insurance_deduction || 0;
    const taxDeduction = grossSalary * ((worker.tax_deduction_percentage || 0) / 100);

    const totalDeductions = advancesDeduction + otherDeductions + insuranceDeduction + taxDeduction;
    const netSalary = grossSalary - totalDeductions;

    return {
      worker_id: workerId,
      payroll_month: month,
      payroll_year: year,
      total_meters_worked: totalMeters,
      total_hours_worked: totalHours,
      overtime_hours: overtimeHours,
      base_salary: baseSalary,
      meter_earnings: meterEarnings,
      overtime_earnings: overtimeEarnings,
      bonuses_total: bonusesTotal,
      gross_salary: grossSalary,
      advances_deduction: advancesDeduction,
      other_deductions: otherDeductions,
      insurance_deduction: insuranceDeduction,
      tax_deduction: taxDeduction,
      total_deductions: totalDeductions,
      net_salary: netSalary
    };
  } catch (error) {
    console.error(`خطأ في حساب مرتب العامل رقم ${workerId}:`, error);
    throw error;
  }
});

ipcMain.handle('monthlyPayrolls:create', async (event, payroll) => {
  try {
    const result = db.prepare(`
      INSERT INTO monthly_payrolls (
        worker_id, payroll_month, payroll_year,
        total_meters_worked, total_hours_worked, overtime_hours,
        base_salary, meter_earnings, overtime_earnings, bonuses_total, gross_salary,
        advances_deduction, other_deductions, insurance_deduction, tax_deduction, total_deductions,
        net_salary, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      payroll.worker_id, payroll.payroll_month, payroll.payroll_year,
      payroll.total_meters_worked, payroll.total_hours_worked, payroll.overtime_hours,
      payroll.base_salary, payroll.meter_earnings, payroll.overtime_earnings, payroll.bonuses_total, payroll.gross_salary,
      payroll.advances_deduction, payroll.other_deductions, payroll.insurance_deduction, payroll.tax_deduction, payroll.total_deductions,
      payroll.net_salary, payroll.notes
    );

    return { id: result.lastInsertRowid, ...payroll };
  } catch (error) {
    console.error('خطأ في إنشاء كشف مرتب:', error);
    throw error;
  }
});

// معالجات الأحداث لفحص الجودة
ipcMain.handle('qualityCheckpoints:getByStageId', async (event, stageId) => {
  try {
    return db.prepare(`
      SELECT * FROM quality_checkpoints
      WHERE stage_id = ?
      ORDER BY check_order
    `).all(stageId);
  } catch (error) {
    console.error(`خطأ في الحصول على نقاط فحص المرحلة رقم ${stageId}:`, error);
    throw error;
  }
});

ipcMain.handle('qualityCheckpoints:create', async (event, checkpoint) => {
  try {
    const result = db.prepare(`
      INSERT INTO quality_checkpoints (
        stage_id, checkpoint_name, description, is_mandatory, check_order
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      checkpoint.stage_id,
      checkpoint.checkpoint_name,
      checkpoint.description,
      checkpoint.is_mandatory ? 1 : 0,
      checkpoint.check_order
    );

    return { id: result.lastInsertRowid, ...checkpoint };
  } catch (error) {
    console.error('خطأ في إنشاء نقطة فحص جودة:', error);
    throw error;
  }
});

ipcMain.handle('qualityChecks:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT qc.*, qcp.checkpoint_name, qcp.description as checkpoint_description,
             ps.stage_name, w1.name as inspector_name, w2.name as approver_name
      FROM quality_checks qc
      JOIN quality_checkpoints qcp ON qc.checkpoint_id = qcp.id
      JOIN production_stages ps ON qc.stage_id = ps.id
      LEFT JOIN workers w1 ON qc.inspector_id = w1.id
      LEFT JOIN workers w2 ON qc.approved_by = w2.id
      WHERE qc.order_id = ?
      ORDER BY ps.stage_order, qcp.check_order
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على فحوصات الجودة للطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('qualityChecks:create', async (event, qualityCheck) => {
  try {
    const result = db.prepare(`
      INSERT INTO quality_checks (
        checkpoint_id, stage_id, order_id, inspector_id, status,
        quality_score, defects_found, corrective_actions, images, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      qualityCheck.checkpoint_id,
      qualityCheck.stage_id,
      qualityCheck.order_id,
      qualityCheck.inspector_id,
      qualityCheck.status || 'قيد الفحص',
      qualityCheck.quality_score,
      qualityCheck.defects_found,
      qualityCheck.corrective_actions,
      qualityCheck.images,
      qualityCheck.notes
    );

    return { id: result.lastInsertRowid, ...qualityCheck };
  } catch (error) {
    console.error('خطأ في إنشاء فحص جودة:', error);
    throw error;
  }
});

ipcMain.handle('qualityChecks:update', async (event, id, qualityCheck) => {
  try {
    db.prepare(`
      UPDATE quality_checks SET
        status = ?, quality_score = ?, defects_found = ?, corrective_actions = ?,
        images = ?, notes = ?, approved_by = ?, approved_at = ?
      WHERE id = ?
    `).run(
      qualityCheck.status,
      qualityCheck.quality_score,
      qualityCheck.defects_found,
      qualityCheck.corrective_actions,
      qualityCheck.images,
      qualityCheck.notes,
      qualityCheck.approved_by,
      qualityCheck.status === 'مقبول' ? new Date().toISOString() : null,
      id
    );

    return { id, ...qualityCheck };
  } catch (error) {
    console.error(`خطأ في تحديث فحص الجودة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('defectsRepairs:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT dr.*, ps.stage_name, qcp.checkpoint_name,
             w1.name as detected_by_name, w2.name as repaired_by_name, w3.name as verified_by_name
      FROM defects_repairs dr
      LEFT JOIN production_stages ps ON dr.stage_id = ps.id
      LEFT JOIN quality_checks qc ON dr.quality_check_id = qc.id
      LEFT JOIN quality_checkpoints qcp ON qc.checkpoint_id = qcp.id
      LEFT JOIN workers w1 ON dr.detected_by = w1.id
      LEFT JOIN workers w2 ON dr.repaired_by = w2.id
      LEFT JOIN workers w3 ON dr.verified_by = w3.id
      WHERE dr.order_id = ?
      ORDER BY dr.detected_date DESC
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على العيوب والإصلاحات للطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('defectsRepairs:create', async (event, defectRepair) => {
  try {
    const result = db.prepare(`
      INSERT INTO defects_repairs (
        order_id, stage_id, quality_check_id, defect_type, defect_description,
        severity, detected_by, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      defectRepair.order_id,
      defectRepair.stage_id,
      defectRepair.quality_check_id,
      defectRepair.defect_type,
      defectRepair.defect_description,
      defectRepair.severity || 'متوسط',
      defectRepair.detected_by,
      defectRepair.notes
    );

    return { id: result.lastInsertRowid, ...defectRepair };
  } catch (error) {
    console.error('خطأ في إنشاء سجل عيب:', error);
    throw error;
  }
});

ipcMain.handle('defectsRepairs:updateRepair', async (event, id, repairData) => {
  try {
    db.prepare(`
      UPDATE defects_repairs SET
        repair_description = ?, repair_cost = ?, repair_time_hours = ?,
        repaired_by = ?, repair_date = ?, status = ?
      WHERE id = ?
    `).run(
      repairData.repair_description,
      repairData.repair_cost || 0,
      repairData.repair_time_hours || 0,
      repairData.repaired_by,
      new Date().toISOString(),
      'مصلح',
      id
    );

    return { id, ...repairData };
  } catch (error) {
    console.error(`خطأ في تحديث إصلاح العيب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('defectsRepairs:verify', async (event, id, verificationData) => {
  try {
    db.prepare(`
      UPDATE defects_repairs SET
        verification_status = ?, verified_by = ?, verified_at = ?
      WHERE id = ?
    `).run(
      verificationData.verification_status,
      verificationData.verified_by,
      new Date().toISOString(),
      id
    );

    return { id, ...verificationData };
  } catch (error) {
    console.error(`خطأ في التحقق من إصلاح العيب رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث للمصنع والتكاليف التشغيلية
ipcMain.handle('factoryExpenses:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM factory_expenses ORDER BY expense_date DESC').all();
  } catch (error) {
    console.error('خطأ في الحصول على مصاريف المصنع:', error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM factory_expenses WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على مصروف المصنع رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:create', async (event, expense) => {
  try {
    const result = db.prepare(
      'INSERT INTO factory_expenses (expense_date, category, description, amount, recurring, period) VALUES (?, ?, ?, ?, ?, ?)'
    ).run(
      expense.expense_date || new Date().toISOString(),
      expense.category,
      expense.description,
      expense.amount,
      expense.recurring ? 1 : 0,
      expense.period
    );

    // إنشاء عملية مالية للمصروف
    db.prepare(`
      INSERT INTO financial_transactions (
        transaction_date, type, category, amount, description, related_expense_id
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      expense.expense_date || new Date().toISOString(),
      'expense',
      expense.category,
      -Math.abs(expense.amount),
      expense.description,
      result.lastInsertRowid
    );

    return { id: result.lastInsertRowid, ...expense };
  } catch (error) {
    console.error('خطأ في إنشاء مصروف مصنع جديد:', error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:update', async (event, id, expense) => {
  try {
    db.prepare(
      'UPDATE factory_expenses SET expense_date = ?, category = ?, description = ?, amount = ?, recurring = ?, period = ? WHERE id = ?'
    ).run(
      expense.expense_date,
      expense.category,
      expense.description,
      expense.amount,
      expense.recurring ? 1 : 0,
      expense.period,
      id
    );

    // تحديث العملية المالية المرتبطة
    db.prepare(`
      UPDATE financial_transactions
      SET transaction_date = ?, category = ?, amount = ?, description = ?
      WHERE related_expense_id = ?
    `).run(
      expense.expense_date,
      expense.category,
      -Math.abs(expense.amount),
      expense.description,
      id
    );

    return { id, ...expense };
  } catch (error) {
    console.error(`خطأ في تحديث مصروف المصنع رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:delete', async (event, id) => {
  try {
    // حذف العملية المالية المرتبطة
    db.prepare('DELETE FROM financial_transactions WHERE related_expense_id = ?').run(id);

    // حذف المصروف
    db.prepare('DELETE FROM factory_expenses WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف مصروف المصنع رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:getByCategory', async (event, category) => {
  try {
    return db.prepare('SELECT * FROM factory_expenses WHERE category = ? ORDER BY expense_date DESC').all(category);
  } catch (error) {
    console.error(`خطأ في الحصول على مصاريف المصنع بفئة "${category}":`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(
      'SELECT * FROM factory_expenses WHERE expense_date BETWEEN ? AND ? ORDER BY expense_date DESC'
    ).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على مصاريف المصنع بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// معالجات الأحداث للفواتير
ipcMain.handle('invoices:getAll', async () => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      ORDER BY i.issue_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الفواتير:', error);
    throw error;
  }
});

ipcMain.handle('invoices:getById', async (event, id) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name, o.specifications
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.id = ?
    `).get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على الفاتورة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.order_id = ?
      ORDER BY i.issue_date DESC
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على فواتير الطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:create', async (event, invoice) => {
  try {
    // إنشاء رقم فاتورة فريد
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    // الحصول على آخر رقم تسلسلي للفواتير في الشهر الحالي
    const lastInvoice = db.prepare(`
      SELECT invoice_number FROM invoices
      WHERE invoice_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`INV-${year}${month}%`);

    let sequenceNumber = 1;
    if (lastInvoice) {
      const lastSequence = parseInt(lastInvoice.invoice_number.split('-')[2]);
      sequenceNumber = lastSequence + 1;
    }

    const invoiceNumber = `INV-${year}${month}-${sequenceNumber.toString().padStart(3, '0')}`;

    // إدخال الفاتورة الجديدة
    const result = db.prepare(`
      INSERT INTO invoices (
        invoice_number, order_id, issue_date, due_date,
        total_amount, paid_amount, status, payment_type, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      invoiceNumber,
      invoice.order_id,
      invoice.issue_date || new Date().toISOString(),
      invoice.due_date,
      invoice.total_amount,
      invoice.paid_amount || 0,
      invoice.status || 'غير مدفوعة',
      invoice.payment_type || 'كامل',
      invoice.notes
    );

    // إنشاء عملية مالية للفاتورة إذا كان هناك مبلغ مدفوع
    if (invoice.paid_amount && invoice.paid_amount > 0) {
      db.prepare(`
        INSERT INTO financial_transactions (
          transaction_date, type, category, amount, description, related_invoice_id, related_order_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        invoice.issue_date || new Date().toISOString(),
        'income',
        'invoice_payment',
        invoice.paid_amount,
        `دفعة من الفاتورة ${invoiceNumber}`,
        result.lastInsertRowid,
        invoice.order_id
      );

      // إنشاء سجل دفع
      db.prepare(`
        INSERT INTO payments (
          invoice_id, payment_date, amount, payment_method, notes
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        result.lastInsertRowid,
        invoice.issue_date || new Date().toISOString(),
        invoice.paid_amount,
        invoice.payment_method || 'نقدي',
        'دفعة أولى'
      );
    }

    return {
      id: result.lastInsertRowid,
      invoice_number: invoiceNumber,
      ...invoice
    };
  } catch (error) {
    console.error('خطأ في إنشاء فاتورة جديدة:', error);
    throw error;
  }
});

// إنشاء فاتورة مع أقساط
ipcMain.handle('invoices:createWithInstallments', async (event, invoice, installmentsData) => {
  try {
    // بدء المعاملة
    db.prepare('BEGIN TRANSACTION').run();

    // إنشاء رقم فاتورة فريد
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    // الحصول على آخر رقم تسلسلي للفواتير في الشهر الحالي
    const lastInvoice = db.prepare(`
      SELECT invoice_number FROM invoices
      WHERE invoice_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`INV-${year}${month}%`);

    let sequenceNumber = 1;
    if (lastInvoice) {
      const lastSequence = parseInt(lastInvoice.invoice_number.split('-')[2]);
      sequenceNumber = lastSequence + 1;
    }

    const invoiceNumber = `INV-${year}${month}-${sequenceNumber.toString().padStart(3, '0')}`;

    // إدخال الفاتورة الجديدة
    const result = db.prepare(`
      INSERT INTO invoices (
        invoice_number, order_id, issue_date, due_date,
        total_amount, paid_amount, status, payment_type, installments_count, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      invoiceNumber,
      invoice.order_id,
      invoice.issue_date || new Date().toISOString(),
      invoice.due_date,
      invoice.total_amount,
      invoice.paid_amount || 0,
      invoice.status || 'غير مدفوعة',
      'أقساط',
      installmentsData.length,
      invoice.notes
    );

    const invoiceId = result.lastInsertRowid;

    // إنشاء الأقساط
    for (let i = 0; i < installmentsData.length; i++) {
      const installment = installmentsData[i];

      db.prepare(`
        INSERT INTO installments (
          invoice_id, installment_number, amount, due_date, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        invoiceId,
        i + 1,
        installment.amount,
        installment.due_date,
        'غير مدفوع',
        installment.notes || `القسط رقم ${i + 1}`
      );
    }

    // إنشاء عملية مالية للفاتورة إذا كان هناك مبلغ مدفوع
    if (invoice.paid_amount && invoice.paid_amount > 0) {
      const paymentResult = db.prepare(`
        INSERT INTO payments (
          invoice_id, payment_date, amount, payment_method, notes
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        invoiceId,
        invoice.issue_date || new Date().toISOString(),
        invoice.paid_amount,
        invoice.payment_method || 'نقدي',
        'دفعة أولى'
      );

      db.prepare(`
        INSERT INTO financial_transactions (
          transaction_date, type, category, amount, description, related_invoice_id, related_order_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        invoice.issue_date || new Date().toISOString(),
        'income',
        'invoice_payment',
        invoice.paid_amount,
        `دفعة أولى للفاتورة ${invoiceNumber}`,
        invoiceId,
        invoice.order_id
      );

      // تحديث القسط الأول إذا كانت الدفعة الأولى تغطيه
      if (installmentsData.length > 0 && invoice.paid_amount >= installmentsData[0].amount) {
        const firstInstallment = db.prepare(`
          SELECT id FROM installments
          WHERE invoice_id = ? AND installment_number = 1
        `).get(invoiceId);

        if (firstInstallment) {
          db.prepare(`
            UPDATE installments
            SET status = ?, payment_date = ?, payment_id = ?
            WHERE id = ?
          `).run(
            'مدفوع',
            invoice.issue_date || new Date().toISOString(),
            paymentResult.lastInsertRowid,
            firstInstallment.id
          );
        }
      }
    }

    // إنهاء المعاملة
    db.prepare('COMMIT').run();

    return {
      id: invoiceId,
      invoice_number: invoiceNumber,
      ...invoice,
      installments: installmentsData
    };
  } catch (error) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    db.prepare('ROLLBACK').run();
    console.error('خطأ في إنشاء فاتورة مع أقساط:', error);
    throw error;
  }
});

ipcMain.handle('invoices:update', async (event, id, invoice) => {
  try {
    // الحصول على الفاتورة الحالية
    const currentInvoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(id);

    db.prepare(`
      UPDATE invoices SET
        order_id = ?, issue_date = ?, due_date = ?,
        total_amount = ?, status = ?, notes = ?
      WHERE id = ?
    `).run(
      invoice.order_id,
      invoice.issue_date,
      invoice.due_date,
      invoice.total_amount,
      invoice.status,
      invoice.notes,
      id
    );

    // تحديث المبلغ المدفوع فقط إذا تغير
    if (currentInvoice && invoice.paid_amount !== currentInvoice.paid_amount) {
      const difference = invoice.paid_amount - currentInvoice.paid_amount;

      if (difference !== 0) {
        // تحديث المبلغ المدفوع في الفاتورة
        db.prepare('UPDATE invoices SET paid_amount = ? WHERE id = ?').run(invoice.paid_amount, id);

        // إنشاء سجل دفع جديد للفرق
        if (difference > 0) {
          db.prepare(`
            INSERT INTO payments (
              invoice_id, payment_date, amount, payment_method, notes
            ) VALUES (?, ?, ?, ?, ?)
          `).run(
            id,
            new Date().toISOString(),
            difference,
            invoice.payment_method || 'نقدي',
            invoice.payment_notes || 'دفعة إضافية'
          );

          // إنشاء عملية مالية للدفعة الجديدة
          db.prepare(`
            INSERT INTO financial_transactions (
              transaction_date, type, category, amount, description, related_invoice_id, related_order_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            new Date().toISOString(),
            'income',
            'invoice_payment',
            difference,
            `دفعة إضافية للفاتورة ${currentInvoice.invoice_number}`,
            id,
            invoice.order_id
          );
        }
      }
    }

    return { id, ...invoice };
  } catch (error) {
    console.error(`خطأ في تحديث الفاتورة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:delete', async (event, id) => {
  try {
    // حذف المدفوعات المرتبطة بالفاتورة
    db.prepare('DELETE FROM payments WHERE invoice_id = ?').run(id);

    // حذف العمليات المالية المرتبطة بالفاتورة
    db.prepare('DELETE FROM financial_transactions WHERE related_invoice_id = ?').run(id);

    // حذف الفاتورة
    db.prepare('DELETE FROM invoices WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الفاتورة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:getByStatus', async (event, status) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.status = ?
      ORDER BY i.issue_date DESC
    `).all(status);
  } catch (error) {
    console.error(`خطأ في الحصول على الفواتير بحالة "${status}":`, error);
    throw error;
  }
});

ipcMain.handle('invoices:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.issue_date BETWEEN ? AND ?
      ORDER BY i.issue_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على الفواتير بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// توليد فاتورة PDF
ipcMain.handle('invoices:generatePDF', async (event, id) => {
  try {
    // الحصول على معلومات الفاتورة
    const invoice = db.prepare(`
      SELECT i.*, o.order_number, o.specifications, o.product_id,
             c.name as customer_name, c.phone as customer_phone, c.address as customer_address,
             p.name as product_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE i.id = ?
    `).get(id);

    if (!invoice) {
      throw new Error('الفاتورة غير موجودة');
    }

    // الحصول على المواد المستخدمة في الطلب
    const materials = db.prepare(`
      SELECT om.*, m.name as material_name, m.unit
      FROM order_materials om
      JOIN materials m ON om.material_id = m.id
      WHERE om.order_id = ?
    `).all(invoice.order_id);

    // الحصول على المدفوعات
    const payments = db.prepare(`
      SELECT *
      FROM payments
      WHERE invoice_id = ?
      ORDER BY payment_date
    `).all(id);

    // الحصول على الأقساط إذا كانت الفاتورة بالأقساط
    let installments = [];
    if (invoice.payment_type === 'أقساط') {
      installments = db.prepare(`
        SELECT *
        FROM installments
        WHERE invoice_id = ?
        ORDER BY installment_number
      `).all(id);
    }

    // إنشاء مسار للملف
    const pdfFileName = `فاتورة_${invoice.invoice_number.replace(/[\/\\:*?"<>|]/g, '_')}.pdf`;
    const downloadsPath = app.getPath('downloads');
    const pdfPath = path.join(downloadsPath, pdfFileName);

    // إنشاء ملف PDF
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      bufferPages: true
    });

    // تكوين الملف للدعم العربي
    doc.registerFont('Arabic', path.join(__dirname, 'assets/fonts/NotoSansArabic-Regular.ttf'));
    doc.font('Arabic');

    // إنشاء تدفق الكتابة
    const stream = fs.createWriteStream(pdfPath);
    doc.pipe(stream);

    // إضافة الترويسة
    doc.fontSize(24).text('اتش قروب', { align: 'center' });
    doc.fontSize(16).text('لصناعة الأثاث والديكور', { align: 'center' });
    doc.moveDown();

    // إضافة معلومات الفاتورة
    doc.fontSize(18).text(`فاتورة رقم: ${invoice.invoice_number}`, { align: 'right' });
    doc.fontSize(12).text(`التاريخ: ${new Date(invoice.issue_date).toLocaleDateString('ar-SA')}`, { align: 'right' });
    doc.fontSize(12).text(`تاريخ الاستحقاق: ${invoice.due_date ? new Date(invoice.due_date).toLocaleDateString('ar-SA') : 'غير محدد'}`, { align: 'right' });
    doc.moveDown();

    // إضافة معلومات العميل
    doc.fontSize(14).text('معلومات العميل:', { align: 'right' });
    doc.fontSize(12).text(`الاسم: ${invoice.customer_name}`, { align: 'right' });
    if (invoice.customer_phone) doc.text(`الهاتف: ${invoice.customer_phone}`, { align: 'right' });
    if (invoice.customer_address) doc.text(`العنوان: ${invoice.customer_address}`, { align: 'right' });
    doc.moveDown();

    // إضافة معلومات الطلب
    doc.fontSize(14).text('معلومات الطلب:', { align: 'right' });
    doc.fontSize(12).text(`رقم الطلب: ${invoice.order_number}`, { align: 'right' });
    doc.fontSize(12).text(`المنتج: ${invoice.product_name || 'غير محدد'}`, { align: 'right' });
    if (invoice.specifications) doc.text(`المواصفات: ${invoice.specifications}`, { align: 'right' });
    doc.moveDown();

    // إضافة جدول المواد المستخدمة
    if (materials.length > 0) {
      doc.fontSize(14).text('المواد المستخدمة:', { align: 'right' });

      // إنشاء جدول
      const materialsTableTop = doc.y + 10;
      const materialsTableWidth = doc.page.width - 100;

      // رسم رأس الجدول
      doc.fontSize(10);
      doc.text('المادة', doc.page.width - 100, materialsTableTop, { width: 150, align: 'right' });
      doc.text('الكمية', doc.page.width - 250, materialsTableTop, { width: 50, align: 'center' });
      doc.text('الوحدة', doc.page.width - 300, materialsTableTop, { width: 50, align: 'center' });
      doc.text('السعر', doc.page.width - 350, materialsTableTop, { width: 50, align: 'center' });
      doc.text('الإجمالي', doc.page.width - 400, materialsTableTop, { width: 50, align: 'center' });

      // رسم خط أفقي
      doc.moveTo(doc.page.width - 450, materialsTableTop + 15)
         .lineTo(doc.page.width - 50, materialsTableTop + 15)
         .stroke();

      // إضافة بيانات المواد
      let materialsTableY = materialsTableTop + 20;

      for (const material of materials) {
        doc.text(material.material_name, doc.page.width - 100, materialsTableY, { width: 150, align: 'right' });
        doc.text(material.quantity.toString(), doc.page.width - 250, materialsTableY, { width: 50, align: 'center' });
        doc.text(material.unit, doc.page.width - 300, materialsTableY, { width: 50, align: 'center' });
        doc.text(material.cost_per_unit.toLocaleString(), doc.page.width - 350, materialsTableY, { width: 50, align: 'center' });
        doc.text(material.total_cost.toLocaleString(), doc.page.width - 400, materialsTableY, { width: 50, align: 'center' });

        materialsTableY += 20;

        // التحقق من الحاجة لصفحة جديدة
        if (materialsTableY > doc.page.height - 100) {
          doc.addPage();
          materialsTableY = 50;
        }
      }

      // رسم خط أفقي
      doc.moveTo(doc.page.width - 450, materialsTableY)
         .lineTo(doc.page.width - 50, materialsTableY)
         .stroke();

      doc.y = materialsTableY + 10;
    }

    // إضافة معلومات الدفع
    doc.fontSize(14).text('معلومات الدفع:', { align: 'right' });

    // إذا كانت الفاتورة بالأقساط
    if (invoice.payment_type === 'أقساط') {
      doc.fontSize(12).text(`نوع الدفع: بالأقساط (${invoice.installments_count} قسط)`, { align: 'right' });

      if (installments.length > 0) {
        // إنشاء جدول الأقساط
        const installmentsTableTop = doc.y + 10;

        // رسم رأس الجدول
        doc.fontSize(10);
        doc.text('رقم القسط', doc.page.width - 100, installmentsTableTop, { width: 50, align: 'center' });
        doc.text('المبلغ', doc.page.width - 200, installmentsTableTop, { width: 100, align: 'center' });
        doc.text('تاريخ الاستحقاق', doc.page.width - 300, installmentsTableTop, { width: 100, align: 'center' });
        doc.text('الحالة', doc.page.width - 400, installmentsTableTop, { width: 100, align: 'center' });

        // رسم خط أفقي
        doc.moveTo(doc.page.width - 450, installmentsTableTop + 15)
           .lineTo(doc.page.width - 50, installmentsTableTop + 15)
           .stroke();

        // إضافة بيانات الأقساط
        let installmentsTableY = installmentsTableTop + 20;

        for (const installment of installments) {
          doc.text(installment.installment_number.toString(), doc.page.width - 100, installmentsTableY, { width: 50, align: 'center' });
          doc.text(installment.amount.toLocaleString(), doc.page.width - 200, installmentsTableY, { width: 100, align: 'center' });
          doc.text(new Date(installment.due_date).toLocaleDateString('ar-SA'), doc.page.width - 300, installmentsTableY, { width: 100, align: 'center' });
          doc.text(installment.status, doc.page.width - 400, installmentsTableY, { width: 100, align: 'center' });

          installmentsTableY += 20;

          // التحقق من الحاجة لصفحة جديدة
          if (installmentsTableY > doc.page.height - 100) {
            doc.addPage();
            installmentsTableY = 50;
          }
        }

        // رسم خط أفقي
        doc.moveTo(doc.page.width - 450, installmentsTableY)
           .lineTo(doc.page.width - 50, installmentsTableY)
           .stroke();

        doc.y = installmentsTableY + 10;
      }
    } else {
      doc.fontSize(12).text('نوع الدفع: دفعة واحدة', { align: 'right' });
    }

    // إضافة ملخص الفاتورة
    doc.moveDown();
    doc.fontSize(14).text('ملخص الفاتورة:', { align: 'right' });
    doc.fontSize(12).text(`إجمالي المبلغ: ${formatCurrency(invoice.total_amount)}`, { align: 'right' });
    doc.fontSize(12).text(`المبلغ المدفوع: ${formatCurrency(invoice.paid_amount)}`, { align: 'right' });
    doc.fontSize(12).text(`المبلغ المتبقي: ${formatCurrency(invoice.total_amount - invoice.paid_amount)}`, { align: 'right' });
    doc.fontSize(12).text(`حالة الفاتورة: ${invoice.status}`, { align: 'right' });

    // إضافة QR Code للفاتورة
    const qrData = JSON.stringify({
      invoiceNumber: invoice.invoice_number,
      customerName: invoice.customer_name,
      totalAmount: invoice.total_amount,
      issueDate: invoice.issue_date
    });

    try {
      const qrDataURL = await QRCode.toDataURL(qrData);
      doc.image(qrDataURL, doc.page.width - 150, doc.page.height - 150, { width: 100 });
    } catch (qrError) {
      console.error('خطأ في إنشاء رمز QR:', qrError);
    }

    // إضافة تذييل الصفحة
    const pageCount = doc.bufferedPageRange().count;
    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i);

      // إضافة رقم الصفحة
      doc.fontSize(8).text(
        `الصفحة ${i + 1} من ${pageCount}`,
        50,
        doc.page.height - 50,
        { align: 'center', width: doc.page.width - 100 }
      );

      // إضافة معلومات الشركة
      doc.fontSize(8).text(
        'اتش قروب لصناعة الأثاث والديكور - جميع الحقوق محفوظة © ' + new Date().getFullYear(),
        50,
        doc.page.height - 40,
        { align: 'center', width: doc.page.width - 100 }
      );
    }

    // إنهاء الملف
    doc.end();

    // انتظار انتهاء الكتابة
    return new Promise((resolve, reject) => {
      stream.on('finish', () => {
        resolve({ success: true, path: pdfPath });
      });

      stream.on('error', (error) => {
        reject(error);
      });
    });
  } catch (error) {
    console.error(`خطأ في إنشاء ملف PDF للفاتورة رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث للمدفوعات
ipcMain.handle('payments:getByInvoiceId', async (event, invoiceId) => {
  try {
    return db.prepare('SELECT * FROM payments WHERE invoice_id = ? ORDER BY payment_date DESC').all(invoiceId);
  } catch (error) {
    console.error(`خطأ في الحصول على مدفوعات الفاتورة رقم ${invoiceId}:`, error);
    throw error;
  }
});

ipcMain.handle('payments:create', async (event, payment) => {
  try {
    const result = db.prepare(
      'INSERT INTO payments (invoice_id, payment_date, amount, payment_method, notes) VALUES (?, ?, ?, ?, ?)'
    ).run(
      payment.invoice_id,
      payment.payment_date || new Date().toISOString(),
      payment.amount,
      payment.payment_method,
      payment.notes
    );

    // تحديث المبلغ المدفوع في الفاتورة
    const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(payment.invoice_id);
    if (invoice) {
      const newPaidAmount = invoice.paid_amount + payment.amount;
      const newStatus = newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً';

      db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
        newPaidAmount,
        newStatus,
        payment.invoice_id
      );

      // إنشاء عملية مالية للدفعة
      db.prepare(`
        INSERT INTO financial_transactions (
          transaction_date, type, category, amount, description, related_invoice_id, related_order_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        payment.payment_date || new Date().toISOString(),
        'income',
        'invoice_payment',
        payment.amount,
        payment.notes || `دفعة للفاتورة ${invoice.invoice_number}`,
        payment.invoice_id,
        invoice.order_id
      );
    }

    return { id: result.lastInsertRowid, ...payment };
  } catch (error) {
    console.error('خطأ في إنشاء دفعة جديدة:', error);
    throw error;
  }
});

ipcMain.handle('payments:update', async (event, id, payment) => {
  try {
    // الحصول على الدفعة الحالية
    const currentPayment = db.prepare('SELECT * FROM payments WHERE id = ?').get(id);

    if (currentPayment) {
      // حساب الفرق في المبلغ
      const amountDifference = payment.amount - currentPayment.amount;

      // تحديث الدفعة
      db.prepare(
        'UPDATE payments SET payment_date = ?, amount = ?, payment_method = ?, notes = ? WHERE id = ?'
      ).run(
        payment.payment_date,
        payment.amount,
        payment.payment_method,
        payment.notes,
        id
      );

      // تحديث المبلغ المدفوع في الفاتورة إذا تغير المبلغ
      if (amountDifference !== 0) {
        const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(payment.invoice_id);
        if (invoice) {
          const newPaidAmount = invoice.paid_amount + amountDifference;
          const newStatus = newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً';

          db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
            newPaidAmount,
            newStatus,
            payment.invoice_id
          );

          // تحديث العملية المالية المرتبطة
          const transaction = db.prepare(
            'SELECT * FROM financial_transactions WHERE description LIKE ? AND related_invoice_id = ? LIMIT 1'
          ).get(`%${currentPayment.notes || 'دفعة'}%`, payment.invoice_id);

          if (transaction) {
            db.prepare(`
              UPDATE financial_transactions
              SET transaction_date = ?, amount = ?, description = ?
              WHERE id = ?
            `).run(
              payment.payment_date,
              payment.amount,
              payment.notes || `دفعة للفاتورة ${invoice.invoice_number}`,
              transaction.id
            );
          }
        }
      }
    }

    return { id, ...payment };
  } catch (error) {
    console.error(`خطأ في تحديث الدفعة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('payments:delete', async (event, id) => {
  try {
    // الحصول على معلومات الدفعة قبل الحذف
    const payment = db.prepare('SELECT * FROM payments WHERE id = ?').get(id);

    if (payment) {
      // حذف الدفعة
      db.prepare('DELETE FROM payments WHERE id = ?').run(id);

      // تحديث المبلغ المدفوع في الفاتورة
      const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(payment.invoice_id);
      if (invoice) {
        const newPaidAmount = Math.max(0, invoice.paid_amount - payment.amount);
        const newStatus = newPaidAmount <= 0 ? 'غير مدفوعة' : (newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً');

        db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
          newPaidAmount,
          newStatus,
          payment.invoice_id
        );

        // حذف العملية المالية المرتبطة
        db.prepare(
          'DELETE FROM financial_transactions WHERE description LIKE ? AND related_invoice_id = ? LIMIT 1'
        ).run(`%${payment.notes || 'دفعة'}%`, payment.invoice_id);
      }
    }

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الدفعة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('payments:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT p.*, i.invoice_number, o.order_number, c.name as customer_name
      FROM payments p
      JOIN invoices i ON p.invoice_id = i.id
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE p.payment_date BETWEEN ? AND ?
      ORDER BY p.payment_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على المدفوعات بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// معالجات الأحداث للأقساط
ipcMain.handle('installments:getByInvoiceId', async (event, invoiceId) => {
  try {
    return db.prepare(`
      SELECT * FROM installments
      WHERE invoice_id = ?
      ORDER BY installment_number
    `).all(invoiceId);
  } catch (error) {
    console.error(`خطأ في الحصول على أقساط الفاتورة رقم ${invoiceId}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM installments WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:create', async (event, installment) => {
  try {
    const result = db.prepare(`
      INSERT INTO installments (
        invoice_id, installment_number, amount, due_date, status, notes
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      installment.invoice_id,
      installment.installment_number,
      installment.amount,
      installment.due_date,
      installment.status || 'غير مدفوع',
      installment.notes
    );

    return { id: result.lastInsertRowid, ...installment };
  } catch (error) {
    console.error('خطأ في إنشاء قسط جديد:', error);
    throw error;
  }
});

ipcMain.handle('installments:update', async (event, id, installment) => {
  try {
    db.prepare(`
      UPDATE installments SET
        amount = ?, due_date = ?, status = ?, notes = ?
      WHERE id = ?
    `).run(
      installment.amount,
      installment.due_date,
      installment.status,
      installment.notes,
      id
    );

    return { id, ...installment };
  } catch (error) {
    console.error(`خطأ في تحديث القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:delete', async (event, id) => {
  try {
    // التحقق من عدم وجود دفعة مرتبطة بالقسط
    const installment = db.prepare('SELECT * FROM installments WHERE id = ?').get(id);
    if (installment && installment.payment_id) {
      throw new Error('لا يمكن حذف القسط لأنه مرتبط بدفعة');
    }

    db.prepare('DELETE FROM installments WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:pay', async (event, id, paymentData) => {
  try {
    // بدء المعاملة
    db.prepare('BEGIN TRANSACTION').run();

    // الحصول على معلومات القسط
    const installment = db.prepare('SELECT * FROM installments WHERE id = ?').get(id);
    if (!installment) {
      throw new Error('القسط غير موجود');
    }

    // الحصول على معلومات الفاتورة
    const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(installment.invoice_id);
    if (!invoice) {
      throw new Error('الفاتورة غير موجودة');
    }

    // إنشاء دفعة جديدة
    const paymentResult = db.prepare(`
      INSERT INTO payments (
        invoice_id, payment_date, amount, payment_method, notes
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      installment.invoice_id,
      paymentData.payment_date || new Date().toISOString(),
      installment.amount,
      paymentData.payment_method || 'نقدي',
      paymentData.notes || `دفعة للقسط رقم ${installment.installment_number}`
    );

    // تحديث حالة القسط
    db.prepare(`
      UPDATE installments SET
        status = ?, payment_date = ?, payment_id = ?
      WHERE id = ?
    `).run(
      'مدفوع',
      paymentData.payment_date || new Date().toISOString(),
      paymentResult.lastInsertRowid,
      id
    );

    // تحديث المبلغ المدفوع في الفاتورة
    const newPaidAmount = invoice.paid_amount + installment.amount;
    const newStatus = newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً';

    db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
      newPaidAmount,
      newStatus,
      installment.invoice_id
    );

    // إنشاء عملية مالية للدفعة
    db.prepare(`
      INSERT INTO financial_transactions (
        transaction_date, type, category, amount, description, related_invoice_id, related_order_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(
      paymentData.payment_date || new Date().toISOString(),
      'income',
      'installment_payment',
      installment.amount,
      `دفعة للقسط رقم ${installment.installment_number} من الفاتورة ${invoice.invoice_number}`,
      installment.invoice_id,
      invoice.order_id
    );

    // إنهاء المعاملة
    db.prepare('COMMIT').run();

    return {
      success: true,
      installment_id: id,
      payment_id: paymentResult.lastInsertRowid,
      amount: installment.amount
    };
  } catch (error) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    db.prepare('ROLLBACK').run();
    console.error(`خطأ في دفع القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:getDueInstallments', async () => {
  try {
    const today = new Date().toISOString().split('T')[0];

    return db.prepare(`
      SELECT i.*, inv.invoice_number, o.order_number, c.name as customer_name
      FROM installments i
      JOIN invoices inv ON i.invoice_id = inv.id
      JOIN orders o ON inv.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.status = 'غير مدفوع' AND i.due_date <= ? AND i.due_date >= ?
      ORDER BY i.due_date
    `).all(today + 'T23:59:59.999Z', today + 'T00:00:00.000Z');
  } catch (error) {
    console.error('خطأ في الحصول على الأقساط المستحقة اليوم:', error);
    throw error;
  }
});

ipcMain.handle('installments:getLateInstallments', async () => {
  try {
    const today = new Date().toISOString().split('T')[0];

    return db.prepare(`
      SELECT i.*, inv.invoice_number, o.order_number, c.name as customer_name
      FROM installments i
      JOIN invoices inv ON i.invoice_id = inv.id
      JOIN orders o ON inv.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.status = 'غير مدفوع' AND i.due_date < ?
      ORDER BY i.due_date
    `).all(today + 'T00:00:00.000Z');
  } catch (error) {
    console.error('خطأ في الحصول على الأقساط المتأخرة:', error);
    throw error;
  }
});

// معالجات الأحداث للإشعارات
ipcMain.handle('notifications:getAll', async () => {
  try {
    return db.prepare(`
      SELECT * FROM notifications
      ORDER BY created_at DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الإشعارات:', error);
    throw error;
  }
});

ipcMain.handle('notifications:getUnread', async () => {
  try {
    return db.prepare(`
      SELECT * FROM notifications
      WHERE read = 0
      ORDER BY created_at DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الإشعارات غير المقروءة:', error);
    throw error;
  }
});

ipcMain.handle('notifications:create', async (event, notification) => {
  try {
    const result = db.prepare(`
      INSERT INTO notifications (
        type, title, message, link, read, created_at
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      notification.type,
      notification.title,
      notification.message,
      notification.link,
      notification.read || 0,
      notification.created_at || new Date().toISOString()
    );

    return { id: result.lastInsertRowid, ...notification };
  } catch (error) {
    console.error('خطأ في إنشاء إشعار جديد:', error);
    throw error;
  }
});

ipcMain.handle('notifications:markAsRead', async (event, id) => {
  try {
    db.prepare(`
      UPDATE notifications
      SET read = 1
      WHERE id = ?
    `).run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في تعليم الإشعار رقم ${id} كمقروء:`, error);
    throw error;
  }
});

ipcMain.handle('notifications:markAllAsRead', async () => {
  try {
    db.prepare(`
      UPDATE notifications
      SET read = 1
      WHERE read = 0
    `).run();

    return { success: true };
  } catch (error) {
    console.error('خطأ في تعليم جميع الإشعارات كمقروءة:', error);
    throw error;
  }
});

ipcMain.handle('notifications:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM notifications WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الإشعار رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('notifications:deleteAll', async () => {
  try {
    db.prepare('DELETE FROM notifications').run();
    return { success: true };
  } catch (error) {
    console.error('خطأ في حذف جميع الإشعارات:', error);
    throw error;
  }
});

// وظيفة تصدير البيانات إلى Excel
ipcMain.handle('export:toExcel', async (event, data, sheetName) => {
  try {
    // إنشاء مصنف Excel جديد
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'اتش قروب';
    workbook.lastModifiedBy = 'اتش قروب';
    workbook.created = new Date();
    workbook.modified = new Date();

    // إضافة ورقة عمل
    const worksheet = workbook.addWorksheet(sheetName || 'البيانات');

    // إعداد رأس الجدول
    if (data.length > 0) {
      const headers = Object.keys(data[0]);
      worksheet.columns = headers.map(header => ({
        header,
        key: header,
        width: 20
      }));

      // تنسيق رأس الجدول
      worksheet.getRow(1).font = {
        bold: true,
        size: 12
      };

      worksheet.getRow(1).alignment = {
        vertical: 'middle',
        horizontal: 'center'
      };

      // إضافة البيانات
      worksheet.addRows(data);

      // تنسيق الخلايا
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
          row.alignment = {
            vertical: 'middle',
            horizontal: 'right'
          };
        }

        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });
    }

    // إنشاء مسار للملف
    const excelFileName = `${sheetName || 'تقرير'}_${new Date().toISOString().split('T')[0]}.xlsx`;
    const downloadsPath = app.getPath('downloads');
    const excelPath = path.join(downloadsPath, excelFileName);

    // حفظ الملف
    await workbook.xlsx.writeFile(excelPath);

    return { success: true, path: excelPath };
  } catch (error) {
    console.error('خطأ في تصدير البيانات إلى Excel:', error);
    throw error;
  }
});

// معالجات الأحداث للعمليات المالية
ipcMain.handle('financialTransactions:getAll', async () => {
  try {
    return db.prepare(`
      SELECT * FROM financial_transactions
      ORDER BY transaction_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على العمليات المالية:', error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM financial_transactions WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على العملية المالية رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:create', async (event, transaction) => {
  try {
    const result = db.prepare(`
      INSERT INTO financial_transactions (
        transaction_date, type, category, amount, description,
        related_order_id, related_worker_id, related_invoice_id, related_expense_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      transaction.transaction_date || new Date().toISOString(),
      transaction.type,
      transaction.category,
      transaction.amount,
      transaction.description,
      transaction.related_order_id || null,
      transaction.related_worker_id || null,
      transaction.related_invoice_id || null,
      transaction.related_expense_id || null
    );

    return { id: result.lastInsertRowid, ...transaction };
  } catch (error) {
    console.error('خطأ في إنشاء عملية مالية جديدة:', error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:update', async (event, id, transaction) => {
  try {
    db.prepare(`
      UPDATE financial_transactions SET
        transaction_date = ?, type = ?, category = ?, amount = ?, description = ?,
        related_order_id = ?, related_worker_id = ?, related_invoice_id = ?, related_expense_id = ?
      WHERE id = ?
    `).run(
      transaction.transaction_date,
      transaction.type,
      transaction.category,
      transaction.amount,
      transaction.description,
      transaction.related_order_id || null,
      transaction.related_worker_id || null,
      transaction.related_invoice_id || null,
      transaction.related_expense_id || null,
      id
    );

    return { id, ...transaction };
  } catch (error) {
    console.error(`خطأ في تحديث العملية المالية رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM financial_transactions WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف العملية المالية رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getByType', async (event, type) => {
  try {
    return db.prepare(
      'SELECT * FROM financial_transactions WHERE type = ? ORDER BY transaction_date DESC'
    ).all(type);
  } catch (error) {
    console.error(`خطأ في الحصول على العمليات المالية بنوع "${type}":`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getByCategory', async (event, category) => {
  try {
    return db.prepare(
      'SELECT * FROM financial_transactions WHERE category = ? ORDER BY transaction_date DESC'
    ).all(category);
  } catch (error) {
    console.error(`خطأ في الحصول على العمليات المالية بفئة "${category}":`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT * FROM financial_transactions
      WHERE transaction_date BETWEEN ? AND ?
      ORDER BY transaction_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على العمليات المالية بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getBalance', async () => {
  try {
    const result = db.prepare('SELECT SUM(amount) as balance FROM financial_transactions').get();
    return { balance: result.balance || 0 };
  } catch (error) {
    console.error('خطأ في الحصول على الرصيد:', error);
    throw error;
  }
});

// معالجات الأحداث للتقارير
ipcMain.handle('reports:getSalesReport', async (event, filters) => {
  try {
    let whereConditions = [];
    let params = [];

    // تطبيق الفلاتر
    if (filters) {
      if (filters.startDate) {
        whereConditions.push('i.issue_date >= ?');
        params.push(filters.startDate);
      }
      if (filters.endDate) {
        whereConditions.push('i.issue_date <= ?');
        params.push(filters.endDate);
      }
      if (filters.customerId) {
        whereConditions.push('c.id = ?');
        params.push(filters.customerId);
      }
      if (filters.productId) {
        whereConditions.push('p.id = ?');
        params.push(filters.productId);
      }
      if (filters.status) {
        whereConditions.push('i.status = ?');
        params.push(filters.status);
      }
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    const salesData = db.prepare(`
      SELECT
        i.id, i.invoice_number, i.issue_date, i.total_amount, i.paid_amount, i.status,
        o.order_number, o.specifications, o.final_price,
        c.name as customer_name, c.phone as customer_phone,
        p.name as product_name, p.category as product_category
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      ${whereClause}
      ORDER BY i.issue_date DESC
    `).all(...params);

    // حساب الإحصائيات الأساسية
    const totalSales = salesData.reduce((sum, sale) => sum + (sale.total_amount || 0), 0);
    const totalOrders = salesData.length;
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;
    const highestSale = salesData.length > 0 ? Math.max(...salesData.map(sale => sale.total_amount || 0)) : 0;

    // تجميع المبيعات حسب الفترة
    const salesByPeriod = [];
    const groupBy = filters?.groupBy || 'month';

    if (salesData.length > 0) {
      const groupedSales = {};

      salesData.forEach(sale => {
        const date = new Date(sale.issue_date);
        let periodKey;

        switch (groupBy) {
          case 'day':
            periodKey = date.toISOString().split('T')[0];
            break;
          case 'week':
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            periodKey = weekStart.toISOString().split('T')[0];
            break;
          case 'month':
            periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case 'year':
            periodKey = date.getFullYear().toString();
            break;
          default:
            periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        }

        if (!groupedSales[periodKey]) {
          groupedSales[periodKey] = {
            period: periodKey,
            orderCount: 0,
            totalSales: 0
          };
        }

        groupedSales[periodKey].orderCount++;
        groupedSales[periodKey].totalSales += sale.total_amount || 0;
      });

      Object.values(groupedSales).forEach(period => {
        period.averageOrderValue = period.orderCount > 0 ? period.totalSales / period.orderCount : 0;
        salesByPeriod.push(period);
      });

      salesByPeriod.sort((a, b) => a.period.localeCompare(b.period));
    }

    // أفضل المنتجات مبيعًا
    const topProducts = [];
    if (salesData.length > 0) {
      const productSales = {};

      salesData.forEach(sale => {
        const productName = sale.product_name || 'غير محدد';
        if (!productSales[productName]) {
          productSales[productName] = {
            productName,
            orderCount: 0,
            totalSales: 0
          };
        }

        productSales[productName].orderCount++;
        productSales[productName].totalSales += sale.total_amount || 0;
      });

      topProducts.push(...Object.values(productSales)
        .sort((a, b) => b.totalSales - a.totalSales)
        .slice(0, 10));
    }

    // أفضل العملاء
    const topCustomers = [];
    if (salesData.length > 0) {
      const customerSales = {};

      salesData.forEach(sale => {
        const customerName = sale.customer_name || 'غير محدد';
        if (!customerSales[customerName]) {
          customerSales[customerName] = {
            customerName,
            orderCount: 0,
            totalSpent: 0
          };
        }

        customerSales[customerName].orderCount++;
        customerSales[customerName].totalSpent += sale.total_amount || 0;
      });

      topCustomers.push(...Object.values(customerSales)
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 10));
    }

    return {
      summary: {
        totalSales,
        totalOrders,
        averageOrderValue,
        highestSale
      },
      salesByPeriod,
      topProducts,
      topCustomers
    };
  } catch (error) {
    console.error('خطأ في توليد تقرير المبيعات:', error);
    throw error;
  }
});

// تصدير تقرير المبيعات إلى Excel
ipcMain.handle('reports:exportSalesReport', async (event, filters) => {
  try {
    // الحصول على بيانات التقرير
    const reportData = await new Promise((resolve, reject) => {
      const handler = require('./main.js');
      try {
        // استدعاء دالة getSalesReport مباشرة
        let whereConditions = [];
        let params = [];

        // تطبيق الفلاتر
        if (filters) {
          if (filters.startDate) {
            whereConditions.push('i.issue_date >= ?');
            params.push(filters.startDate);
          }
          if (filters.endDate) {
            whereConditions.push('i.issue_date <= ?');
            params.push(filters.endDate);
          }
          if (filters.customerId) {
            whereConditions.push('c.id = ?');
            params.push(filters.customerId);
          }
          if (filters.productId) {
            whereConditions.push('p.id = ?');
            params.push(filters.productId);
          }
          if (filters.status) {
            whereConditions.push('i.status = ?');
            params.push(filters.status);
          }
        }

        const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

        const salesData = db.prepare(`
          SELECT
            i.id, i.invoice_number, i.issue_date, i.total_amount, i.paid_amount, i.status,
            o.order_number, o.specifications, o.final_price,
            c.name as customer_name, c.phone as customer_phone,
            p.name as product_name, p.category as product_category
          FROM invoices i
          JOIN orders o ON i.order_id = o.id
          JOIN customers c ON o.customer_id = c.id
          LEFT JOIN products p ON o.product_id = p.id
          ${whereClause}
          ORDER BY i.issue_date DESC
        `).all(...params);

        // حساب الإحصائيات الأساسية
        const totalSales = salesData.reduce((sum, sale) => sum + (sale.total_amount || 0), 0);
        const totalOrders = salesData.length;
        const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;
        const highestSale = salesData.length > 0 ? Math.max(...salesData.map(sale => sale.total_amount || 0)) : 0;

        // تجميع المبيعات حسب الفترة
        const salesByPeriod = [];
        const groupBy = filters?.groupBy || 'month';

        if (salesData.length > 0) {
          const groupedSales = {};

          salesData.forEach(sale => {
            const date = new Date(sale.issue_date);
            let periodKey;

            switch (groupBy) {
              case 'day':
                periodKey = date.toISOString().split('T')[0];
                break;
              case 'week':
                const weekStart = new Date(date);
                weekStart.setDate(date.getDate() - date.getDay());
                periodKey = weekStart.toISOString().split('T')[0];
                break;
              case 'month':
                periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                break;
              case 'year':
                periodKey = date.getFullYear().toString();
                break;
              default:
                periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            }

            if (!groupedSales[periodKey]) {
              groupedSales[periodKey] = {
                period: periodKey,
                orderCount: 0,
                totalSales: 0
              };
            }

            groupedSales[periodKey].orderCount++;
            groupedSales[periodKey].totalSales += sale.total_amount || 0;
          });

          Object.values(groupedSales).forEach(period => {
            period.averageOrderValue = period.orderCount > 0 ? period.totalSales / period.orderCount : 0;
            salesByPeriod.push(period);
          });

          salesByPeriod.sort((a, b) => a.period.localeCompare(b.period));
        }

        resolve({
          summary: {
            totalSales,
            totalOrders,
            averageOrderValue,
            highestSale
          },
          salesByPeriod,
          topProducts: [],
          topCustomers: []
        });
      } catch (error) {
        reject(error);
      }
    });

    // إنشاء مصنف Excel جديد
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('تقرير المبيعات');

    // إعداد رأس الجدول
    worksheet.columns = [
      { header: 'الفترة', key: 'period', width: 15 },
      { header: 'عدد الطلبات', key: 'orderCount', width: 15 },
      { header: 'إجمالي المبيعات', key: 'totalSales', width: 20 },
      { header: 'متوسط قيمة الطلب', key: 'averageOrderValue', width: 20 }
    ];

    // إضافة البيانات
    if (reportData.salesByPeriod && reportData.salesByPeriod.length > 0) {
      reportData.salesByPeriod.forEach(period => {
        worksheet.addRow({
          period: period.period,
          orderCount: period.orderCount,
          totalSales: period.totalSales,
          averageOrderValue: period.averageOrderValue
        });
      });
    }

    // تنسيق الجدول
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // حفظ الملف
    const { dialog } = require('electron');
    const path = require('path');

    const result = await dialog.showSaveDialog({
      title: 'حفظ تقرير المبيعات',
      defaultPath: path.join(require('os').homedir(), 'تقرير_المبيعات.xlsx'),
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] }
      ]
    });

    if (!result.canceled && result.filePath) {
      await workbook.xlsx.writeFile(result.filePath);
      return { success: true, path: result.filePath };
    } else {
      return { success: false, message: 'تم إلغاء العملية' };
    }
  } catch (error) {
    console.error('خطأ في تصدير تقرير المبيعات:', error);
    throw error;
  }
});

ipcMain.handle('reports:getSalesByPeriod', async (event, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setHours(0, 0, 0, 0);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    return db.prepare(`
      SELECT
        i.id, i.invoice_number, i.issue_date, i.total_amount, i.paid_amount,
        o.order_number, o.specifications,
        c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      ${dateFilter}
      ORDER BY i.issue_date DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير المبيعات لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getExpensesByPeriod', async (event, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setHours(0, 0, 0, 0);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    return db.prepare(`
      SELECT * FROM financial_transactions
      ${dateFilter} AND type = 'expense'
      ORDER BY transaction_date DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير المصروفات لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getProfitByPeriod', async (event, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setHours(0, 0, 0, 0);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const income = db.prepare(`
      SELECT SUM(amount) as total FROM financial_transactions
      ${dateFilter} AND type = 'income'
    `).get();

    const expenses = db.prepare(`
      SELECT SUM(amount) as total FROM financial_transactions
      ${dateFilter} AND type = 'expense'
    `).get();

    const incomeTotal = income.total || 0;
    const expensesTotal = Math.abs(expenses.total || 0);
    const profit = incomeTotal - expensesTotal;

    return {
      income: incomeTotal,
      expenses: expensesTotal,
      profit: profit
    };
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير الأرباح لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getWorkerPerformance', async (event, workerId, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `AND ow.assigned_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `AND ow.assigned_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `AND ow.assigned_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const workerFilter = workerId ? `AND ow.worker_id = ${workerId}` : '';

    return db.prepare(`
      SELECT
        w.id as worker_id, w.name as worker_name, w.role,
        COUNT(ow.id) as total_orders,
        SUM(ow.fee) as total_fees,
        COUNT(CASE WHEN ow.status = 'مكتمل' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN ow.status = 'قيد التنفيذ' THEN 1 END) as in_progress_orders
      FROM workers w
      LEFT JOIN order_workers ow ON w.id = ow.worker_id
      WHERE 1=1 ${workerFilter} ${dateFilter}
      GROUP BY w.id
      ORDER BY total_orders DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير أداء العمال لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getMaterialUsage', async (event, materialId, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const materialFilter = materialId ? `AND om.material_id = ${materialId}` : '';

    return db.prepare(`
      SELECT
        m.id as material_id, m.name as material_name, m.unit,
        SUM(om.quantity) as total_quantity,
        SUM(om.total_cost) as total_cost,
        COUNT(DISTINCT om.order_id) as order_count
      FROM materials m
      LEFT JOIN order_materials om ON m.id = om.material_id
      LEFT JOIN orders o ON om.order_id = o.id
      WHERE 1=1 ${materialFilter} ${dateFilter}
      GROUP BY m.id
      ORDER BY total_quantity DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير استخدام المواد لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getProductPerformance', async (event, productId, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const productFilter = productId ? `AND o.product_id = ${productId}` : '';

    return db.prepare(`
      SELECT
        p.id as product_id, p.name as product_name, p.category,
        COUNT(o.id) as order_count,
        SUM(o.final_price) as total_sales,
        SUM(o.materials_cost) as total_materials_cost,
        SUM(o.worker_fee) as total_worker_fees,
        SUM(o.factory_fee) as total_factory_fees,
        SUM(o.designer_fee) as total_designer_fees,
        SUM(o.owner_margin) as total_margin
      FROM products p
      LEFT JOIN orders o ON p.id = o.product_id
      WHERE 1=1 ${productFilter} ${dateFilter}
      GROUP BY p.id
      ORDER BY total_sales DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير أداء المنتجات لفترة "${period}":`, error);
    throw error;
  }
});

// معالجات الأحداث للمستخدمين
let currentUser = null;

ipcMain.handle('users:login', async (event, username, password) => {
  try {
    const user = db.prepare('SELECT * FROM users WHERE username = ?').get(username);

    if (!user) {
      throw new Error('اسم المستخدم غير موجود');
    }

    if (user.password !== password) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    // تحديث آخر تسجيل دخول
    db.prepare('UPDATE users SET last_login = ? WHERE id = ?').run(new Date().toISOString(), user.id);

    // تسجيل العملية في سجل العمليات
    db.prepare(
      'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
    ).run(user.id, 'login', 'تسجيل دخول للنظام');

    // حفظ المستخدم الحالي
    currentUser = {
      id: user.id,
      username: user.username,
      full_name: user.full_name,
      role: user.role,
      permissions: user.permissions
    };

    return currentUser;
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    throw error;
  }
});

ipcMain.handle('users:logout', async () => {
  try {
    if (currentUser) {
      // تسجيل العملية في سجل العمليات
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'logout', 'تسجيل خروج من النظام');

      currentUser = null;
    }

    return { success: true };
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    throw error;
  }
});

ipcMain.handle('users:getCurrentUser', async () => {
  return currentUser;
});

ipcMain.handle('users:getAll', async () => {
  try {
    return db.prepare('SELECT id, username, full_name, role, permissions, last_login, created_at FROM users').all();
  } catch (error) {
    console.error('خطأ في الحصول على المستخدمين:', error);
    throw error;
  }
});

ipcMain.handle('users:getById', async (event, id) => {
  try {
    return db.prepare('SELECT id, username, full_name, role, permissions, last_login, created_at FROM users WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على المستخدم رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('users:create', async (event, user) => {
  try {
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = db.prepare('SELECT id FROM users WHERE username = ?').get(user.username);
    if (existingUser) {
      throw new Error('اسم المستخدم موجود بالفعل');
    }

    const result = db.prepare(
      'INSERT INTO users (username, password, full_name, role, permissions) VALUES (?, ?, ?, ?, ?)'
    ).run(
      user.username,
      user.password,
      user.full_name,
      user.role || 'user',
      user.permissions || ''
    );

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'create_user', `إنشاء مستخدم جديد: ${user.username}`);
    }

    return {
      id: result.lastInsertRowid,
      username: user.username,
      full_name: user.full_name,
      role: user.role || 'user',
      permissions: user.permissions || ''
    };
  } catch (error) {
    console.error('خطأ في إنشاء مستخدم جديد:', error);
    throw error;
  }
});

ipcMain.handle('users:update', async (event, id, user) => {
  try {
    // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
    const existingUser = db.prepare('SELECT id FROM users WHERE username = ? AND id != ?').get(user.username, id);
    if (existingUser) {
      throw new Error('اسم المستخدم موجود بالفعل');
    }

    const updateQuery = user.password
      ? 'UPDATE users SET username = ?, password = ?, full_name = ?, role = ?, permissions = ? WHERE id = ?'
      : 'UPDATE users SET username = ?, full_name = ?, role = ?, permissions = ? WHERE id = ?';

    const params = user.password
      ? [user.username, user.password, user.full_name, user.role, user.permissions, id]
      : [user.username, user.full_name, user.role, user.permissions, id];

    db.prepare(updateQuery).run(...params);

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'update_user', `تحديث بيانات المستخدم: ${user.username}`);
    }

    // تحديث المستخدم الحالي إذا كان هو نفسه
    if (currentUser && currentUser.id === id) {
      currentUser = {
        id,
        username: user.username,
        full_name: user.full_name,
        role: user.role,
        permissions: user.permissions
      };
    }

    return {
      id,
      username: user.username,
      full_name: user.full_name,
      role: user.role,
      permissions: user.permissions
    };
  } catch (error) {
    console.error(`خطأ في تحديث المستخدم رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('users:delete', async (event, id) => {
  try {
    // التحقق من عدم حذف المستخدم الحالي
    if (currentUser && currentUser.id === id) {
      throw new Error('لا يمكن حذف المستخدم الحالي');
    }

    // التحقق من عدم حذف المستخدم الوحيد بدور المدير
    const user = db.prepare('SELECT role FROM users WHERE id = ?').get(id);
    if (user && user.role === 'admin') {
      const adminCount = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = "admin"').get();
      if (adminCount.count <= 1) {
        throw new Error('لا يمكن حذف المستخدم الوحيد بدور المدير');
      }
    }

    // حذف سجل العمليات المرتبطة بالمستخدم
    db.prepare('DELETE FROM activity_log WHERE user_id = ?').run(id);

    // حذف المستخدم
    db.prepare('DELETE FROM users WHERE id = ?').run(id);

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'delete_user', `حذف المستخدم رقم: ${id}`);
    }

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف المستخدم رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('users:changePassword', async (event, id, oldPassword, newPassword) => {
  try {
    // التحقق من كلمة المرور القديمة
    const user = db.prepare('SELECT password FROM users WHERE id = ?').get(id);
    if (!user) {
      throw new Error('المستخدم غير موجود');
    }

    if (user.password !== oldPassword) {
      throw new Error('كلمة المرور القديمة غير صحيحة');
    }

    // تحديث كلمة المرور
    db.prepare('UPDATE users SET password = ? WHERE id = ?').run(newPassword, id);

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'change_password', `تغيير كلمة المرور للمستخدم رقم: ${id}`);
    }

    return { success: true };
  } catch (error) {
    console.error(`خطأ في تغيير كلمة المرور للمستخدم رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث لسجل العمليات
ipcMain.handle('activityLog:getAll', async () => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      ORDER BY a.action_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على سجل العمليات:', error);
    throw error;
  }
});

ipcMain.handle('activityLog:getByUserId', async (event, userId) => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      WHERE a.user_id = ?
      ORDER BY a.action_date DESC
    `).all(userId);
  } catch (error) {
    console.error(`خطأ في الحصول على سجل عمليات المستخدم رقم ${userId}:`, error);
    throw error;
  }
});

ipcMain.handle('activityLog:getByActionType', async (event, actionType) => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      WHERE a.action_type = ?
      ORDER BY a.action_date DESC
    `).all(actionType);
  } catch (error) {
    console.error(`خطأ في الحصول على سجل العمليات بنوع "${actionType}":`, error);
    throw error;
  }
});

ipcMain.handle('activityLog:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      WHERE a.action_date BETWEEN ? AND ?
      ORDER BY a.action_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على سجل العمليات بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// معالجات الأحداث للإعدادات
ipcMain.handle('settings:get', async (event, key) => {
  try {
    return store.get(key);
  } catch (error) {
    console.error(`خطأ في الحصول على الإعداد "${key}":`, error);
    throw error;
  }
});

ipcMain.handle('settings:set', async (event, key, value) => {
  try {
    store.set(key, value);
    return { success: true, key, value };
  } catch (error) {
    console.error(`خطأ في تعيين الإعداد "${key}":`, error);
    throw error;
  }
});

ipcMain.handle('settings:getAll', async () => {
  try {
    return store.store;
  } catch (error) {
    console.error('خطأ في الحصول على جميع الإعدادات:', error);
    throw error;
  }
});

ipcMain.handle('settings:backup', async (event, path) => {
  try {
    // إغلاق قاعدة البيانات مؤقتاً
    if (db) {
      db.close();
    }

    // نسخ ملف قاعدة البيانات
    const dbPath = app.getPath('userData') + '/hgroup.db';
    fs.copyFileSync(dbPath, path);

    // إعادة فتح قاعدة البيانات
    db = new sqlite3(app.getPath('userData') + '/hgroup.db');

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'backup', `إنشاء نسخة احتياطية في: ${path}`);
    }

    return { success: true, path };
  } catch (error) {
    console.error(`خطأ في إنشاء نسخة احتياطية في "${path}":`, error);
    throw error;
  }
});

ipcMain.handle('settings:restore', async (event, path) => {
  try {
    // إغلاق قاعدة البيانات مؤقتاً
    if (db) {
      db.close();
    }

    // استعادة ملف قاعدة البيانات
    const dbPath = app.getPath('userData') + '/hgroup.db';
    fs.copyFileSync(path, dbPath);

    // إعادة فتح قاعدة البيانات
    db = new sqlite3(app.getPath('userData') + '/hgroup.db');

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'restore', `استعادة نسخة احتياطية من: ${path}`);
    }

    return { success: true, path };
  } catch (error) {
    console.error(`خطأ في استعادة نسخة احتياطية من "${path}":`, error);
    throw error;
  }
});
